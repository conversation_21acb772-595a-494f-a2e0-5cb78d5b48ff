import request from '@/utils/request'

/**
 * 获取系统配置
 * @param keys 配置键数组
 * @returns 配置值对象
 */
export function getSystemConfig(keys: string[]) {
  return request({
    url: '/admin/system/config/batch',
    method: 'post',
    data: { keys }
  })
}

/**
 * 更新系统配置
 * @param data 配置数据对象
 * @returns 更新结果
 */
export function updateSystemConfig(data: Record<string, string>) {
  return request({
    url: '/admin/system/config/batch',
    method: 'put',
    data
  })
}

/**
 * 获取所有系统配置
 * @returns 所有配置列表
 */
export function getAllSystemConfig() {
  return request({
    url: '/admin/system/config/list',
    method: 'get'
  })
}

/**
 * 获取配置详情
 * @param id 配置ID
 * @returns 配置详情
 */
export function getConfigDetail(id: number) {
  return request({
    url: `/admin/system/config/${id}`,
    method: 'get'
  })
}

/**
 * 添加配置
 * @param data 配置数据
 * @returns 添加结果
 */
export function addConfig(data: any) {
  return request({
    url: '/admin/system/config',
    method: 'post',
    data
  })
}

/**
 * 更新配置
 * @param id 配置ID
 * @param data 配置数据
 * @returns 更新结果
 */
export function updateConfig(id: number, data: any) {
  return request({
    url: `/admin/system/config/${id}`,
    method: 'put',
    data
  })
}

/**
 * 删除配置
 * @param id 配置ID
 * @returns 删除结果
 */
export function deleteConfig(id: number) {
  return request({
    url: `/admin/system/config/${id}`,
    method: 'delete'
  })
}
