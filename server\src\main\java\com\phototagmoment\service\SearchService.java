package com.phototagmoment.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.phototagmoment.dto.PhotoDTO;
import com.phototagmoment.dto.TagDTO;
import com.phototagmoment.dto.UserDTO;

import java.util.List;
import java.util.Map;

/**
 * 搜索服务接口
 */
public interface SearchService {

    /**
     * 搜索照片
     *
     * @param keyword 关键词
     * @param page 页码
     * @param size 每页大小
     * @param sort 排序方式：recent-最新，popular-热门
     * @param userId 当前用户ID
     * @return 照片分页结果
     */
    IPage<PhotoDTO> searchPhotos(String keyword, int page, int size, String sort, Long userId);

    /**
     * 搜索用户
     *
     * @param keyword 关键词
     * @param page 页码
     * @param size 每页大小
     * @param userId 当前用户ID
     * @return 用户分页结果
     */
    IPage<UserDTO> searchUsers(String keyword, int page, int size, Long userId);

    /**
     * 搜索标签
     *
     * @param keyword 关键词
     * @param limit 数量限制
     * @return 标签列表
     */
    List<TagDTO> searchTags(String keyword, int limit);

    /**
     * 综合搜索
     *
     * @param keyword 关键词
     * @param page 页码
     * @param size 每页大小
     * @param sort 排序方式：recent-最新，popular-热门
     * @param type 搜索类型：all-全部，photos-照片，users-用户，tags-标签
     * @param userId 当前用户ID
     * @return 搜索结果
     */
    Map<String, Object> search(String keyword, int page, int size, String sort, String type, Long userId);

    /**
     * 获取热门标签
     *
     * @param limit 数量限制
     * @return 热门标签列表
     */
    List<TagDTO> getPopularTags(int limit);

    /**
     * 获取推荐用户
     *
     * @param limit 数量限制
     * @param userId 当前用户ID
     * @return 推荐用户列表
     */
    List<UserDTO> getRecommendedUsers(int limit, Long userId);
}
