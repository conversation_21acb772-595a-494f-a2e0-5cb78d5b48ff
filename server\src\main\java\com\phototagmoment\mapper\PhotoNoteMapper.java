package com.phototagmoment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.phototagmoment.dto.PhotoNoteDTO;
import com.phototagmoment.entity.PhotoNote;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;
import java.util.Map;

/**
 * 照片笔记Mapper接口
 */
@Mapper
public interface PhotoNoteMapper extends BaseMapper<PhotoNote> {

    /**
     * 分页查询照片笔记列表
     *
     * @param page 分页参数
     * @param userId 用户ID（可选，查询指定用户的笔记）
     * @param currentUserId 当前用户ID
     * @param status 状态筛选
     * @return 照片笔记列表
     */
    IPage<PhotoNoteDTO> selectPhotoNotePage(Page<PhotoNoteDTO> page,
                                           @Param("userId") Long userId,
                                           @Param("currentUserId") Long currentUserId,
                                           @Param("status") Integer status);

    /**
     * 管理端分页查询照片笔记列表（无可见性限制）
     *
     * @param page 分页参数
     * @param userId 用户ID（可选，查询指定用户的笔记）
     * @param status 状态筛选
     * @return 照片笔记列表
     */
    IPage<PhotoNoteDTO> selectAdminPhotoNotePage(Page<PhotoNoteDTO> page,
                                                @Param("userId") Long userId,
                                                @Param("status") Integer status);

    /**
     * 查询照片笔记详情
     *
     * @param noteId 照片笔记ID
     * @param currentUserId 当前用户ID
     * @return 照片笔记详情
     */
    PhotoNoteDTO selectPhotoNoteDetail(@Param("noteId") Long noteId,
                                      @Param("currentUserId") Long currentUserId);

    /**
     * 根据标签查询照片笔记
     *
     * @param page 分页参数
     * @param tagName 标签名称
     * @param currentUserId 当前用户ID
     * @param sortType 排序类型：hot-热度排序，time-时间排序
     * @return 照片笔记列表
     */
    IPage<PhotoNoteDTO> selectPhotoNotesByTag(Page<PhotoNoteDTO> page,
                                             @Param("tagName") String tagName,
                                             @Param("currentUserId") Long currentUserId,
                                             @Param("sortType") String sortType);

    /**
     * 查询用户的照片笔记
     *
     * @param page 分页参数
     * @param userId 用户ID
     * @param currentUserId 当前用户ID
     * @param visibility 可见性筛选
     * @return 照片笔记列表
     */
    IPage<PhotoNoteDTO> selectUserPhotoNotes(Page<PhotoNoteDTO> page,
                                            @Param("userId") Long userId,
                                            @Param("currentUserId") Long currentUserId,
                                            @Param("visibility") Integer visibility);

    /**
     * 增加照片笔记浏览数
     *
     * @param noteId 照片笔记ID
     * @return 影响行数
     */
    @Update("UPDATE ptm_photo_note SET view_count = view_count + 1 WHERE id = #{noteId}")
    int incrementViewCount(@Param("noteId") Long noteId);

    /**
     * 增加照片笔记点赞数
     *
     * @param noteId 照片笔记ID
     * @return 影响行数
     */
    @Update("UPDATE ptm_photo_note SET like_count = like_count + 1 WHERE id = #{noteId}")
    int incrementLikeCount(@Param("noteId") Long noteId);

    /**
     * 减少照片笔记点赞数
     *
     * @param noteId 照片笔记ID
     * @return 影响行数
     */
    @Update("UPDATE ptm_photo_note SET like_count = like_count - 1 WHERE id = #{noteId} AND like_count > 0")
    int decrementLikeCount(@Param("noteId") Long noteId);

    /**
     * 增加照片笔记评论数
     *
     * @param noteId 照片笔记ID
     * @return 影响行数
     */
    @Update("UPDATE ptm_photo_note SET comment_count = comment_count + 1 WHERE id = #{noteId}")
    int incrementCommentCount(@Param("noteId") Long noteId);

    /**
     * 减少照片笔记评论数
     *
     * @param noteId 照片笔记ID
     * @return 影响行数
     */
    @Update("UPDATE ptm_photo_note SET comment_count = comment_count - 1 WHERE id = #{noteId} AND comment_count > 0")
    int decrementCommentCount(@Param("noteId") Long noteId);

    /**
     * 检查用户是否已点赞照片笔记
     *
     * @param noteId 照片笔记ID
     * @param userId 用户ID
     * @return 是否已点赞
     */
    @Select("SELECT COUNT(*) > 0 FROM ptm_photo_note_like WHERE note_id = #{noteId} AND user_id = #{userId}")
    boolean checkUserLiked(@Param("noteId") Long noteId, @Param("userId") Long userId);

    /**
     * 检查用户是否已收藏照片笔记
     *
     * @param noteId 照片笔记ID
     * @param userId 用户ID
     * @return 是否已收藏
     */
    @Select("SELECT COUNT(*) > 0 FROM ptm_photo_note_collection WHERE note_id = #{noteId} AND user_id = #{userId}")
    boolean checkUserCollected(@Param("noteId") Long noteId, @Param("userId") Long userId);

    /**
     * 查询热门照片笔记
     *
     * @param page 分页参数
     * @param days 最近天数
     * @param currentUserId 当前用户ID
     * @return 热门照片笔记列表
     */
    IPage<PhotoNoteDTO> selectHotPhotoNotes(Page<PhotoNoteDTO> page,
                                           @Param("days") Integer days,
                                           @Param("currentUserId") Long currentUserId);

    /**
     * 查询推荐照片笔记
     *
     * @param page 分页参数
     * @param userId 用户ID
     * @return 推荐照片笔记列表
     */
    IPage<PhotoNoteDTO> selectRecommendedPhotoNotes(Page<PhotoNoteDTO> page,
                                                   @Param("userId") Long userId);

    /**
     * 搜索照片笔记
     *
     * @param page 分页参数
     * @param keyword 关键词
     * @param currentUserId 当前用户ID
     * @return 搜索结果
     */
    IPage<PhotoNoteDTO> searchPhotoNotes(Page<PhotoNoteDTO> page,
                                        @Param("keyword") String keyword,
                                        @Param("currentUserId") Long currentUserId);

    /**
     * 查询关注用户的照片笔记
     *
     * @param page 分页参数
     * @param followingIds 关注的用户ID列表
     * @param currentUserId 当前用户ID
     * @param lastId 最后一条记录ID（用于游标分页）
     * @return 关注用户的照片笔记列表
     */
    IPage<PhotoNoteDTO> selectFollowingPhotoNotes(Page<PhotoNoteDTO> page,
                                                 @Param("followingIds") List<Long> followingIds,
                                                 @Param("currentUserId") Long currentUserId,
                                                 @Param("lastId") Long lastId);

    /**
     * 查询最新照片笔记（支持游标分页）
     *
     * @param page 分页参数
     * @param currentUserId 当前用户ID
     * @param lastId 最后一条记录ID（用于游标分页）
     * @return 最新照片笔记列表
     */
    IPage<PhotoNoteDTO> selectLatestPhotoNotes(Page<PhotoNoteDTO> page,
                                              @Param("currentUserId") Long currentUserId,
                                              @Param("lastId") Long lastId);

    /**
     * 查询热门照片笔记（支持游标分页）
     *
     * @param page 分页参数
     * @param days 最近天数
     * @param currentUserId 当前用户ID
     * @param lastId 最后一条记录ID（用于游标分页）
     * @return 热门照片笔记列表
     */
    IPage<PhotoNoteDTO> selectHotPhotoNotes(Page<PhotoNoteDTO> page,
                                           @Param("days") Integer days,
                                           @Param("currentUserId") Long currentUserId,
                                           @Param("lastId") Long lastId);

    /**
     * 查询推荐照片笔记（混合推荐算法）
     *
     * @param page 分页参数
     * @param userId 用户ID
     * @param interestTags 用户兴趣标签
     * @param followingIds 关注的用户ID列表
     * @param lastId 最后一条记录ID（用于游标分页）
     * @return 推荐照片笔记列表
     */
    IPage<PhotoNoteDTO> selectRecommendedPhotoNotes(Page<PhotoNoteDTO> page,
                                                   @Param("userId") Long userId,
                                                   @Param("interestTags") List<String> interestTags,
                                                   @Param("followingIds") List<Long> followingIds,
                                                   @Param("lastId") Long lastId);

    /**
     * 获取照片笔记的标签列表
     *
     * @param noteId 照片笔记ID
     * @return 标签列表
     */
    @Select("SELECT tag_name FROM ptm_photo_note_tag WHERE note_id = #{noteId}")
    List<String> getPhotoNoteTags(@Param("noteId") Long noteId);

    /**
     * 查询照片笔记统计信息
     *
     * @return 照片笔记统计信息
     */
    @Select("SELECT " +
            "COUNT(*) as totalNotes, " +
            "COUNT(CASE WHEN status = 1 THEN 1 END) as publishedNotes, " +
            "COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) as todayNewNotes, " +
            "SUM(view_count) as totalViews, " +
            "SUM(like_count) as totalLikes, " +
            "SUM(comment_count) as totalComments " +
            "FROM ptm_photo_note WHERE is_deleted = 0")
    Map<String, Object> selectPhotoNoteStatistics();

    /**
     * 查询内容分布统计
     *
     * @return 内容分布统计
     */
    @Select("SELECT " +
            "visibility, " +
            "COUNT(*) as count " +
            "FROM ptm_photo_note " +
            "WHERE is_deleted = 0 AND status = 1 " +
            "GROUP BY visibility")
    List<Map<String, Object>> selectContentDistribution();

    /**
     * 查询最新照片笔记
     *
     * @param limit 数量限制
     * @return 最新照片笔记列表
     */
    @Select("SELECT id, title, content, user_id, view_count, like_count, comment_count, created_at " +
            "FROM ptm_photo_note " +
            "WHERE is_deleted = 0 AND status = 1 " +
            "ORDER BY created_at DESC " +
            "LIMIT #{limit}")
    List<PhotoNote> selectLatestPhotos(@Param("limit") Integer limit);
}
