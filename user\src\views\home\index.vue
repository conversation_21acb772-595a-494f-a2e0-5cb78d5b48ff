<template>
  <div class="home-container">
    <div class="home-layout">
      <!-- 左侧热门话题 -->
      <div class="left-sidebar">
        <div class="sidebar-header">
          <h3>热门话题</h3>
        </div>

        <!-- 热门话题列表 -->
        <div class="hot-topics-list">
          <div v-if="loadingTopics" class="loading-topics">
            <van-loading type="spinner" color="#ff2442" size="24px" />
          </div>
          <div v-else-if="hotTopics.length === 0" class="empty-topics">
            <van-empty image-size="60" description="暂无热门话题" />
          </div>
          <div v-else class="topic-items">
            <div
              v-for="(topic, index) in hotTopics"
              :key="index"
              class="topic-item"
              @click="handleTopicClick(topic)"
            >
              <span class="topic-rank">{{ index + 1 }}</span>
              <span class="topic-name">{{ topic.name }}</span>
              <span class="topic-count">{{ topic.count }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧主内容区域 -->
      <div class="main-content">
        <!-- 搜索框 - 在移动端显示 -->
        <div class="search-bar" v-if="isMobile">
          <van-search
            v-model="searchQuery"
            placeholder="搜索照片、用户或标签"
            shape="round"
            @focus="showSearchPopup = true"
            readonly
          />
        </div>

        <!-- PC端顶部搜索框 -->
        <div class="top-search-bar" v-if="!isMobile">
          <van-search
            v-model="searchQuery"
            placeholder="搜索照片、用户或标签"
            shape="round"
            @focus="showSearchPopup = true"
            readonly
          />
        </div>

        <!-- 统一导航栏 - 仅在PC端显示 -->
        <div class="main-nav" v-if="!isMobile">
          <!-- 推荐标签 - 固定选中 -->
          <!-- <div class="nav-item">
            推荐
          </div> -->

          <!-- 分类标签 -->
          <div
            v-for="category in categories"
            :key="category.value"
            :class="['nav-item', { active: activeCategory === category.value }]"
            @click="handleCategoryChange(category.value)"
          >
            {{ category.label }}
          </div>

          <!-- 发布按钮 - PC端导航栏右侧 -->
          <div class="nav-publish-button" @click="handlePublish">
            <van-icon name="plus" size="16" />
            <span>发布</span>
          </div>
        </div>

        <!-- 搜索弹出层 - 移动端全屏 -->
        <van-popup
          v-if="isMobile"
          v-model:show="showSearchPopup"
          position="top"
          :style="{ height: '100%' }"
          closeable
          close-icon-position="top-left"
          @closed="onSearchPopupClosed"
        >
          <div class="search-popup-content">
            <van-search
              v-model="searchQuery"
              placeholder="搜索照片、用户或标签"
              show-action
              autofocus
              @search="onSearch"
              @cancel="showSearchPopup = false"
            />

            <!-- 搜索历史 -->
            <div v-if="!searchQuery" class="search-history">
              <div class="history-header">
                <h3>搜索历史</h3>
                <van-button plain size="small" @click="clearSearchHistory">清空</van-button>
              </div>
              <div class="history-list">
                <div
                  v-for="(item, index) in searchHistory"
                  :key="index"
                  class="history-item"
                  @click="searchWithHistory(item)"
                >
                  <van-icon name="clock-o" />
                  <span>{{ item }}</span>
                </div>
              </div>
            </div>

            <!-- 热门搜索 -->
            <div v-if="!searchQuery" class="hot-search">
              <h3>热门搜索</h3>
              <div class="tag-list">
                <van-tag
                  v-for="(tag, index) in hotTags"
                  :key="index"
                  class="tag-item"
                  @click="searchWithTag(tag)"
                >
                  {{ tag }}
                </van-tag>
              </div>
            </div>
          </div>
        </van-popup>

        <!-- PC端搜索弹出层 - 下拉框形式 -->
        <van-popup
          v-if="!isMobile"
          v-model:show="showSearchPopup"
          position="top"
          :style="{ width: '500px', maxHeight: '500px', top: '70px', left: '50%', transform: 'translateX(-50%)' }"
          round
          @closed="onSearchPopupClosed"
        >
          <div class="pc-search-popup">
            <!-- 搜索框 -->
            <div class="pc-search-input">
              <van-search
                v-model="searchQuery"
                placeholder="搜索照片、用户或标签"
                shape="round"
                autofocus
                @search="onSearch"
              />
            </div>

            <div class="pc-search-content">
              <!-- 热门搜索 -->
              <div class="pc-search-section">
                <div class="pc-section-header">热门搜索</div>
                <div class="pc-hot-tags">
                  <div
                    v-for="(tag, index) in hotTags.slice(0, 6)"
                    :key="index"
                    class="pc-hot-tag"
                    @click="searchWithTag(tag)"
                  >
                    {{ tag }}
                  </div>
                </div>
              </div>

              <!-- 搜索历史 -->
              <div class="pc-search-section" v-if="searchHistory.length > 0">
                <div class="pc-section-header">
                  <span>搜索历史</span>
                  <span class="pc-clear-history" @click="clearSearchHistory">清空</span>
                </div>
                <div class="pc-history-list">
                  <div
                    v-for="(item, index) in searchHistory.slice(0, 5)"
                    :key="index"
                    class="pc-history-item"
                    @click="searchWithHistory(item)"
                  >
                    <van-icon name="clock-o" />
                    <span>{{ item }}</span>
                  </div>
                </div>
              </div>

              <!-- 分类导航 -->
              <div class="pc-search-section">
                <div class="pc-section-header">分类导航</div>
                <div class="pc-categories">
                  <div
                    v-for="category in categories.slice(0, 8)"
                    :key="category.value"
                    class="pc-category-item"
                    @click="handleCategoryChange(category.value)"
                  >
                    {{ category.label }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </van-popup>

        <div class="content-container">
          <!-- 照片网格 -->
          <div class="grid-container">
            <div v-if="loading" class="loading-container">
              <van-loading type="spinner" color="#ff2442" size="36px" />
              <div class="loading-text">加载中...</div>
            </div>
            <div v-else-if="photoList.length === 0" class="empty-container">
              <van-empty description="暂无内容" />
            </div>
            <div v-else class="photo-grid">
              <div
                v-for="photo in photoList"
                :key="photo.id"
                class="grid-item"
                @click="handlePhotoClick(photo)"
              >
                <div class="photo-card">
                  <div class="photo-image">
                    <img :src="photo.url" :alt="photo.title" loading="lazy" />
                    <!-- 分组照片标识 -->
                    <div v-if="photo.isGrouped && photo.groupPhotos && photo.groupPhotos.length > 1" class="photo-group-badge">
                      <van-icon name="photo-o" />
                      <span>{{ photo.groupPhotos.length }}</span>
                    </div>
                  </div>
                  <div class="photo-info">
                    <div class="photo-title">{{ photo.title }}</div>
                    <div class="photo-user" @click.stop="goToUserProfile(photo.userId || (photo.user && photo.user.id))">
                      <img
                        :src="photo.userAvatar || (photo.user && photo.user.avatar) || 'https://randomuser.me/api/portraits/men/1.jpg'"
                        :alt="photo.userNickname || photo.userName || (photo.user && (photo.user.nickname || photo.user.username)) || '未知用户'"
                        class="user-avatar"
                      />
                      <span class="user-name">{{ photo.userNickname || photo.userName || (photo.user && (photo.user.nickname || photo.user.username)) || '未知用户' }}</span>
                    </div>
                    <div class="photo-stats">
                      <span class="stat-item">
                        <van-icon name="like-o" />
                        {{ photo.likeCount }}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 加载更多 -->
            <div v-if="photoList.length > 0 && hasMore" class="load-more">
              <van-button plain type="default" :loading="loadingMore" @click="loadMore">加载更多</van-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 发布按钮 - 仅在移动端底部显示 -->
    <div class="publish-button" v-if="isMobile" @click="handlePublish">
      <van-icon name="plus" size="20" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, computed, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'
import { getHomeRecommendations, getFollowingPhotos, getHotPhotos, getRecommendedPhotos, recordUserBehavior, getUserInterestTags } from '@/api/recommendation'
import { useUserStore } from '@/stores/user'
import { getHotTags } from '@/api/tag'
import { getAllCategories } from '@/api/category'

const router = useRouter()
const userStore = useUserStore()

// 检查用户是否已登录
const isLoggedIn = computed(() => userStore.isLoggedIn)

// 是否为移动设备
const isMobile = ref(false)

// 检测设备类型
const checkDeviceType = () => {
  isMobile.value = window.innerWidth < 768
}

// 搜索相关
const searchQuery = ref('')
const showSearchPopup = ref(false)
const searchHistory = ref<string[]>([])
const hotTags = ref<string[]>([])

// 监听窗口大小变化
onMounted(() => {
  checkDeviceType()
  window.addEventListener('resize', checkDeviceType)

  // 加载热门标签
  loadHotSearchTags()
})

onUnmounted(() => {
  window.removeEventListener('resize', checkDeviceType)
})

// 加载热门搜索标签
const loadHotSearchTags = async () => {
  try {
    const res = await getHotTags(10)
    if (res && res.data) {
      hotTags.value = res.data.map((tag: any) => tag.name || tag)
      console.log('热门搜索标签数据:', hotTags.value)
    }
  } catch (error) {
    console.error('加载热门搜索标签失败', error)
    // 使用默认标签作为备用
    hotTags.value = []
  }
}

// 左侧边栏不再需要标签页

// 顶部导航标签页 - 固定为推荐，不再需要tabs数组
const activeTab = ref('home')

// 分类标签
const categories = ref<any[]>([
  { label: '推荐', value: 'recommend' } // 默认推荐选项
])
const activeCategory = ref('')

// 热门话题
interface Topic {
  name: string;
  count: number;
}
const hotTopics = ref<Topic[]>([])
const loadingTopics = ref(false)

// 照片列表
interface PhotoUser {
  id: number;
  username: string;
  nickname: string;
  avatar: string;
}

interface Photo {
  id: number;
  title: string;
  url: string;
  thumbnailUrl: string;
  userName: string;
  userNickname?: string;
  userAvatar: string;
  userId: number;
  viewCount: number;
  likeCount: number;
  commentCount: number;
  uploadTime: string;
  tags: string[];
  groupId?: string; // 照片分组ID
  groupPhotos?: Photo[]; // 同组照片
  isGrouped?: boolean; // 是否为分组照片
  user?: PhotoUser; // 用户信息对象
}

const photoList = ref<Photo[]>([])
const loading = ref(false)
const loadingMore = ref(false)
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const hasMore = ref(false)

// 不再需要瀑布流左右列数据，使用网格布局

// 加载分类
const loadCategories = async () => {
  try {
    const res = await getAllCategories()
    if (res && res.data) {
      // 保留推荐选项，并添加从服务器获取的分类
      categories.value = [
        { label: '推荐', value: 'recommend' },
        ...res.data.map((cat: any) => ({
          label: cat.dictLabel || cat.name,
          value: cat.dictValue || cat.value || cat.id?.toString()
        }))
      ]
    }
  } catch (error) {
    console.error('加载分类失败', error)
    // 使用默认分类作为备用
    categories.value = [
      { label: '推荐', value: 'recommend' }
    ]
  }
}

// 初始化
onMounted(() => {
  // 默认加载推荐内容，不选中任何分类
  activeTab.value = 'home'
  activeCategory.value = 'recommend'
  loadPhotos()
  loadHotTopics()
  loadCategories()
})

// 监听标签页变化
watch(activeTab, () => {
  photoList.value = []
  currentPage.value = 1
  loadPhotos()
})

// 监听分类变化
watch(activeCategory, () => {
  photoList.value = []
  currentPage.value = 1
  loadPhotos()
})

// 加载照片
const loadPhotos = async () => {
  try {
    loading.value = true
    const params = {
      page: currentPage.value,
      size: pageSize.value,
      category: activeCategory.value || undefined
    }

    let res: any
    switch (activeTab.value) {
      case 'home':
        res = await getHomeRecommendations(params)
        break
      case 'following':
        res = await getFollowingPhotos(params)
        break
      case 'hot':
        res = await getHotPhotos(params)
        break
      case 'recommended':
        res = await getRecommendedPhotos(params)
        break
      default:
        res = await getHomeRecommendations(params)
    }

    if (res.code === 200) {
      // 适配数据结构
      const photos = res.data.records.map((item: any) => {
        // 创建用户对象，确保PhotoCard组件可以正确访问
        const user = {
          id: item.userId || 0,
          username: item.userName || 'Unknown',
          nickname: item.userNickname || item.userName || 'Unknown',
          avatar: item.userAvatar || 'https://randomuser.me/api/portraits/men/1.jpg'
        };

        return {
          id: item.id,
          title: item.title,
          url: item.url || item.thumbnailUrl,
          thumbnailUrl: item.thumbnailUrl,
          userName: item.userName || 'Unknown',
          userNickname: item.userNickname || '',
          userAvatar: item.userAvatar || 'https://randomuser.me/api/portraits/men/1.jpg',
          userId: item.userId || 0,
          viewCount: item.viewCount || 0,
          likeCount: item.likeCount || 0,
          commentCount: item.commentCount || 0,
          uploadTime: item.uploadTime || item.createdAt || new Date().toISOString(),
          tags: item.tags || [],
          groupId: item.groupId || null,
          isGrouped: !!item.groupId,
          // 添加用户对象，供PhotoCard组件使用
          user: user
        };
      });

      // 处理照片分组
      const groupedPhotos: Photo[] = [];
      const groupMap = new Map<string, Photo[]>();

      // 将照片按分组ID分类
      photos.forEach((photo: Photo) => {
        if (photo.groupId) {
          if (!groupMap.has(photo.groupId)) {
            groupMap.set(photo.groupId, []);
          }
          groupMap.get(photo.groupId)?.push(photo);
        } else {
          groupedPhotos.push(photo);
        }
      });

      // 处理每个分组，只保留第一张照片作为封面
      groupMap.forEach((groupPhotos, groupId) => {
        if (groupPhotos.length > 0) {
          const coverPhoto = groupPhotos[0];
          coverPhoto.groupPhotos = groupPhotos;
          coverPhoto.isGrouped = true;
          groupedPhotos.push(coverPhoto);
        }
      });

      photoList.value = groupedPhotos
      total.value = res.data.total
      hasMore.value = currentPage.value * pageSize.value < total.value
    }
  } catch (error) {
    console.error('加载照片失败', error)
    showToast('加载照片失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 加载更多
const loadMore = async () => {
  try {
    loadingMore.value = true
    currentPage.value++
    const params = {
      page: currentPage.value,
      size: pageSize.value,
      category: activeCategory.value || undefined
    }

    let res: any
    switch (activeTab.value) {
      case 'home':
        res = await getHomeRecommendations(params)
        break
      case 'following':
        res = await getFollowingPhotos(params)
        break
      case 'hot':
        res = await getHotPhotos(params)
        break
      case 'recommended':
        res = await getRecommendedPhotos(params)
        break
      default:
        res = await getHomeRecommendations(params)
    }

    if (res.code === 200) {
      // 适配数据结构
      const newPhotos = res.data.records.map((item: any) => ({
        id: item.id,
        title: item.title,
        url: item.url || item.thumbnailUrl,
        thumbnailUrl: item.thumbnailUrl,
        userName: item.userName || item.user?.nickname || 'Unknown',
        userAvatar: item.userAvatar || item.user?.avatar || 'https://randomuser.me/api/portraits/men/1.jpg',
        userId: item.userId || item.user?.id || 0,
        viewCount: item.viewCount || 0,
        likeCount: item.likeCount || 0,
        commentCount: item.commentCount || 0,
        uploadTime: item.uploadTime || item.createdAt || new Date().toISOString(),
        tags: item.tags || [],
        groupId: item.groupId || null,
        isGrouped: !!item.groupId
      }));

      // 处理照片分组
      const groupedPhotos: Photo[] = [];
      const groupMap = new Map<string, Photo[]>();

      // 将照片按分组ID分类
      newPhotos.forEach((photo: Photo) => {
        if (photo.groupId) {
          if (!groupMap.has(photo.groupId)) {
            groupMap.set(photo.groupId, []);
          }
          groupMap.get(photo.groupId)?.push(photo);
        } else {
          groupedPhotos.push(photo);
        }
      });

      // 处理每个分组，只保留第一张照片作为封面
      groupMap.forEach((groupPhotos, groupId) => {
        if (groupPhotos.length > 0) {
          const coverPhoto = groupPhotos[0];
          coverPhoto.groupPhotos = groupPhotos;
          coverPhoto.isGrouped = true;
          groupedPhotos.push(coverPhoto);
        }
      });

      photoList.value = [...photoList.value, ...groupedPhotos]
      total.value = res.data.total
      hasMore.value = currentPage.value * pageSize.value < total.value
    }
  } catch (error) {
    console.error('加载更多照片失败', error)
    showToast('加载更多照片失败，请稍后重试')
  } finally {
    loadingMore.value = false
  }
}

// 不再需要侧边栏标签页切换处理和标签页切换处理

// 处理分类切换
const handleCategoryChange = (category: string) => {
  // 如果点击的是当前选中的分类，则取消选中
  if (activeCategory.value === category) {
    activeCategory.value = ''
  } else {
    activeCategory.value = category
  }
}

// 加载热门话题
const loadHotTopics = async () => {
  try {
    loadingTopics.value = true
    // 使用用户兴趣标签接口获取热门话题
    const res = await getUserInterestTags({ limit: 15 })

    if (res && res.data) {
      // 将标签数据转换为话题对象
      hotTopics.value = res.data.map((tag: any) => ({
        name: typeof tag === 'string' ? tag : (tag.name || ''),
        count: Math.floor(Math.random() * 1000) // 生成随机数作为计数
      })).slice(0, 15) // 确保最多只显示15条

      console.log('热门话题数据:', hotTopics.value)
    }
  } catch (error) {
    console.error('加载热门话题失败', error)
    // 使用少量模拟数据作为备用
    hotTopics.value = []
  } finally {
    loadingTopics.value = false
  }
}

// 处理话题点击
const handleTopicClick = (topic: Topic) => {
  // 跳转到搜索页面，搜索该话题
  router.push({
    path: '/search',
    query: { keyword: topic.name }
  })
}

// 处理发布按钮点击
const handlePublish = () => {
  // 检查用户是否已登录
  if (!isLoggedIn.value) {
    showToast('请先登录')
    router.push('/auth/login')
    return
  }

  // 跳转到上传页面
  router.push('/upload')
}

// 处理照片点击
const handlePhotoClick = (photo: any) => {
  // 如果用户已登录，记录用户行为
  if (isLoggedIn.value) {
    recordUserBehavior({
      photoId: photo.id,
      behavior: 'view'
    })
  }

  // 跳转到照片详情页
  router.push(`/photo/detail/${photo.id}`)
}

// 跳转到用户个人页面
const goToUserProfile = (userId: number) => {
  if (!userId) {
    showToast('用户信息不完整')
    return
  }

  // 阻止事件冒泡，避免触发照片点击事件
  event?.stopPropagation()

  // 跳转到用户个人页面
  router.push(`/user/${userId}`)
}

// 搜索相关方法
const onSearch = () => {
  if (searchQuery.value.trim()) {
    // 添加到搜索历史
    if (!searchHistory.value.includes(searchQuery.value)) {
      searchHistory.value.unshift(searchQuery.value)
      if (searchHistory.value.length > 10) {
        searchHistory.value.pop()
      }
    }

    // 跳转到搜索页面
    router.push({
      path: '/search',
      query: { keyword: searchQuery.value }
    })

    // 关闭搜索弹窗
    showSearchPopup.value = false
  }
}

// 使用历史记录搜索
const searchWithHistory = (query: string) => {
  searchQuery.value = query
  onSearch()
}

// 使用标签搜索
const searchWithTag = (tag: string) => {
  searchQuery.value = tag
  onSearch()
}

// 清空搜索历史
const clearSearchHistory = () => {
  searchHistory.value = []
}

// 搜索弹窗关闭后的处理
const onSearchPopupClosed = () => {
  // 重置搜索查询
  searchQuery.value = ''
}
</script>

<style scoped>
.home-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #fff;
  position: relative;
}

/* 主布局 */
.home-layout {
  display: flex;
  min-height: calc(100vh - 50px);
}

/* 左侧边栏样式 */
.left-sidebar {
  width: 350px;
  flex-shrink: 0;
  background-color: #fff;
  border-right: 1px solid #f0f0f0;
  padding: 20px 0;
  height: 100vh;
  position: sticky;
  top: 0;
  overflow-y: auto;
}

.sidebar-header {
  padding: 0 20px;
  margin-bottom: 20px;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 10px;
}

.sidebar-header h3 {
  font-size: 16px;
  color: #333;
  margin: 0;
  font-weight: 600;
}

.hot-topics-list {
  padding: 0 20px;
}

.loading-topics,
.empty-topics {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px 0;
}

.topic-items {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.topic-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
  cursor: pointer;
  transition: all 0.2s;
  border-radius: 4px;
}

.topic-item:hover {
  background-color: #f5f5f5;
  padding-left: 8px;
}

.topic-rank {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  margin-right: 8px;
  color: #fff;
  background-color: #ff2442;
  border-radius: 4px;
}

.topic-item:nth-child(n+4) .topic-rank {
  background-color: #ff9999;
}

.topic-item:nth-child(n+10) .topic-rank {
  background-color: #ffcccc;
  color: #ff2442;
}

.topic-name {
  flex: 1;
  font-size: 14px;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.topic-count {
  font-size: 12px;
  color: #999;
  margin-left: 8px;
}

/* 主内容区域 */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* 主导航样式 */
.search-bar {
  padding: 10px 15px;
  background-color: #fff;
  position: sticky;
  top: 0;
  z-index: 15;
}

/* PC端顶部搜索框样式 */
.top-search-bar {
  padding: 5px 20px;
  background-color: #fff;
  border-bottom: 1px solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 15;
}

.top-search-bar :deep(.van-search) {
  max-width: 600px;
  margin: 0 auto;
}

.top-search-bar :deep(.van-search__content) {
  background-color: #f5f5f5;
  border-radius: 20px;
  height: 40px;
}

.top-search-bar :deep(.van-field__left-icon) {
  margin-right: 10px;
}

.top-search-bar :deep(.van-field__control) {
  font-size: 15px;
}

.main-nav {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  border-bottom: 1px solid #f0f0f0;
  background-color: #fff;
  position: sticky;
  top: 70px; /* 调整位置，在搜索框下方 */
  z-index: 14; /* 降低z-index，让搜索框在上层 */
  overflow-x: auto;
  white-space: nowrap;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.main-nav::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

/* 统一导航项样式 */
.nav-item {
  display: inline-block;
  font-size: 14px;
  color: #666;
  cursor: pointer;
  padding: 4px 12px;
  margin-right: 10px;
  border-radius: 16px;
  background-color: #f5f5f5;
  transition: all 0.2s;
}

.nav-item.active {
  color: #fff;
  background-color: #ff2442;
}

/* 导航栏右侧发布按钮 */
.nav-publish-button {
  margin-left: auto;
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 6px 15px;
  border-radius: 16px;
  background-color: #ff2442;
  color: #fff;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.nav-publish-button:hover {
  background-color: #e61f3c;
}

/* 搜索弹出层样式 */
.search-popup-content {
  padding: 10px 0;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.search-history,
.hot-search {
  padding: 15px;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.history-header h3,
.hot-search h3 {
  font-size: 16px;
  font-weight: 500;
  margin: 0;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.history-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #666;
  cursor: pointer;
}

.history-item .van-icon {
  font-size: 16px;
  color: #999;
}

.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 10px;
}

.tag-item {
  cursor: pointer;
  padding: 6px 12px;
  font-size: 14px;
}

/* PC端搜索弹出层样式 */
.pc-search-popup {
  padding: 15px;
  max-height: 500px;
  overflow-y: auto;
}

.pc-search-input {
  margin-bottom: 15px;
}

.pc-search-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.pc-search-section {
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 15px;
}

.pc-search-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.pc-section-header {
  font-size: 14px;
  color: #999;
  margin-bottom: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pc-clear-history {
  font-size: 12px;
  color: #666;
  cursor: pointer;
}

.pc-clear-history:hover {
  color: #ff2442;
}

.pc-hot-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.pc-hot-tag {
  padding: 6px 12px;
  background-color: #f5f5f5;
  border-radius: 16px;
  font-size: 13px;
  color: #333;
  cursor: pointer;
  transition: all 0.2s;
}

.pc-hot-tag:hover {
  background-color: #ffe0e5;
  color: #ff2442;
}

.pc-history-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.pc-history-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #666;
  cursor: pointer;
  padding: 5px 0;
}

.pc-history-item:hover {
  color: #ff2442;
}

.pc-history-item .van-icon {
  font-size: 14px;
  color: #999;
}

.pc-categories {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.pc-category-item {
  padding: 6px 12px;
  background-color: #f5f5f5;
  border-radius: 16px;
  font-size: 13px;
  color: #333;
  cursor: pointer;
  transition: all 0.2s;
}

.pc-category-item:hover {
  background-color: #ffe0e5;
  color: #ff2442;
}

/* 分类标签栏样式 - 已合并到导航栏中，此处样式不再使用 */

/* 内容区域样式 */
.content-container {
  flex: 1;
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

/* 照片网格区域 */
.grid-container {
  flex: 1;
  padding-bottom: 20px;
}

.photo-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
}

@media (min-width: 992px) {
  .photo-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1200px) {
  .photo-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

.grid-item {
  width: 100%;
  break-inside: avoid;
}

.photo-card {
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s;
  cursor: pointer;
}

.photo-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.photo-image {
  width: 100%;
  position: relative;
  overflow: hidden;
  aspect-ratio: 1 / 1;
}

.photo-image img {
  width: 100%;
  height: 100%;
  display: block;
  object-fit: cover;
  transition: transform 0.3s;
}

/* 分组照片标识 */
.photo-group-badge {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: rgba(0, 0, 0, 0.6);
  color: #fff;
  border-radius: 16px;
  padding: 4px 8px;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.photo-group-badge .van-icon {
  font-size: 14px;
}

.photo-card:hover .photo-image img {
  transform: scale(1.03);
}

.photo-info {
  padding: 12px;
}

.photo-title {
  font-size: 14px;
  line-height: 1.5;
  color: #333;
  margin-bottom: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}

.photo-user {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.user-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  margin-right: 8px;
  object-fit: cover;
}

.user-name {
  font-size: 12px;
  color: #666;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.photo-stats {
  display: flex;
  align-items: center;
}

.stat-item {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #999;
}

.stat-item .van-icon {
  margin-right: 4px;
  font-size: 14px;
}

/* 加载状态 */
.loading-container,
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
}

.loading-text {
  margin-top: 12px;
  color: #999;
  font-size: 14px;
}

/* 加载更多 */
.load-more {
  display: flex;
  justify-content: center;
  margin: 24px 0;
}

.load-more .van-button {
  border-radius: 20px;
  font-size: 14px;
  padding: 0 24px;
  height: 36px;
  border-color: #ddd;
  color: #666;
}

/* 发布按钮 */
.publish-button {
  position: fixed;
  right: 30px;
  bottom: 30px;
  width: 56px;
  height: 56px;
  background-color: #ff2442;
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(255, 36, 66, 0.4);
  cursor: pointer;
  z-index: 100;
  transition: all 0.3s;
}

.publish-button:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 16px rgba(255, 36, 66, 0.5);
}

/* 响应式设计 */
@media (max-width: 992px) {
  .home-layout {
    flex-direction: column;
  }

  .left-sidebar {
    display: none; /* 移动端不显示左侧热门话题 */
  }
}

/* 响应式设计 - 移动端 */
@media (max-width: 768px) {
  .main-nav {
    padding: 8px 12px;
  }

  .top-search-bar {
    display: none; /* 移动端不显示PC版搜索框 */
  }

  /* 移动端不显示分类栏，已合并到导航栏 */

  .content-container {
    padding: 12px;
  }

  .topic-items {
    grid-template-columns: 1fr;
  }

  .photo-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
  }

  .photo-info {
    padding: 8px;
  }

  .photo-title {
    font-size: 13px;
    margin-bottom: 6px;
  }

  .user-avatar {
    width: 20px;
    height: 20px;
    margin-right: 6px;
  }

  .publish-button {
    display: none; /* 移动端不显示发布按钮 */
  }
}
</style>
