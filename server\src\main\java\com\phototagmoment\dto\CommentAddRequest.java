package com.phototagmoment.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 添加评论请求
 */
@Data
@Schema(description = "添加评论请求")
public class CommentAddRequest {

    @Schema(description = "照片ID")
    @NotNull(message = "照片ID不能为空")
    private Long photoId;

    @Schema(description = "评论内容")
    @NotBlank(message = "评论内容不能为空")
    private String content;
}
