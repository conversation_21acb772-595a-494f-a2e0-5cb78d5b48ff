<template>
  <div class="tag-management">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>标签管理</span>
          <div class="header-actions">
            <el-button type="primary" @click="refreshData">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <!-- 搜索筛选 -->
      <div class="search-section">
        <el-form :model="searchForm" inline>
          <el-form-item label="标签名称">
            <el-input
              v-model="searchForm.tagName"
              placeholder="请输入标签名称"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          
          <el-form-item label="排序方式">
            <el-select
              v-model="searchForm.sortBy"
              placeholder="请选择排序方式"
              style="width: 150px"
            >
              <el-option label="热度排序" value="hotScore" />
              <el-option label="使用次数" value="useCount" />
              <el-option label="笔记数量" value="noteCount" />
              <el-option label="最后使用时间" value="lastUsedAt" />
            </el-select>
          </el-form-item>

          <el-form-item label="最小使用次数">
            <el-input-number
              v-model="searchForm.minUseCount"
              :min="0"
              placeholder="最小使用次数"
              style="width: 150px"
            />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 批量操作 -->
      <div v-if="selectedRows.length > 0" class="batch-actions">
        <el-alert
          :title="`已选择 ${selectedRows.length} 个标签`"
          type="info"
          show-icon
          :closable="false"
        />
        <div class="batch-buttons">
          <el-button type="danger" @click="batchDelete">
            批量删除
          </el-button>
          <el-button type="primary" @click="showMergeDialog">
            合并标签
          </el-button>
        </div>
      </div>

      <!-- 数据表格 -->
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        
        <el-table-column prop="tagName" label="标签名称" width="200">
          <template #default="scope">
            <el-tag type="primary" size="large">
              #{{ scope.row.tagName }}#
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="useCount" label="使用次数" width="120" sortable>
          <template #default="scope">
            <el-statistic :value="scope.row.useCount" />
          </template>
        </el-table-column>

        <el-table-column prop="noteCount" label="笔记数量" width="120" sortable>
          <template #default="scope">
            <el-statistic :value="scope.row.noteCount" />
          </template>
        </el-table-column>

        <el-table-column prop="totalViewCount" label="总浏览量" width="120" sortable>
          <template #default="scope">
            <el-statistic :value="scope.row.totalViewCount" />
          </template>
        </el-table-column>

        <el-table-column prop="totalLikeCount" label="总点赞数" width="120" sortable>
          <template #default="scope">
            <el-statistic :value="scope.row.totalLikeCount" />
          </template>
        </el-table-column>

        <el-table-column prop="hotScore" label="热度分数" width="120" sortable>
          <template #default="scope">
            <el-tag :type="getHotScoreType(scope.row.hotScore)">
              {{ scope.row.hotScore?.toFixed(2) || '0.00' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="lastUsedAt" label="最后使用时间" width="180">
          <template #default="scope">
            {{ formatDateTime(scope.row.lastUsedAt) }}
          </template>
        </el-table-column>

        <el-table-column prop="createdAt" label="创建时间" width="180">
          <template #default="scope">
            {{ formatDateTime(scope.row.createdAt) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              @click="viewTagDetail(scope.row)"
            >
              查看详情
            </el-button>
            
            <el-button
              type="warning"
              size="small"
              @click="showMergeDialog([scope.row])"
            >
              合并
            </el-button>
            
            <el-button
              type="danger"
              size="small"
              @click="deleteTag(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 标签详情弹窗 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="标签详情"
      width="800px"
    >
      <div v-if="currentTag" class="tag-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="标签名称">
            <el-tag type="primary" size="large">
              #{{ currentTag.tagName }}#
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="热度分数">
            <el-tag :type="getHotScoreType(currentTag.hotScore)">
              {{ currentTag.hotScore?.toFixed(2) || '0.00' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="使用次数">
            {{ currentTag.useCount }}
          </el-descriptions-item>
          <el-descriptions-item label="笔记数量">
            {{ currentTag.noteCount }}
          </el-descriptions-item>
          <el-descriptions-item label="总浏览量">
            {{ currentTag.totalViewCount }}
          </el-descriptions-item>
          <el-descriptions-item label="总点赞数">
            {{ currentTag.totalLikeCount }}
          </el-descriptions-item>
          <el-descriptions-item label="最后使用时间">
            {{ formatDateTime(currentTag.lastUsedAt) }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ formatDateTime(currentTag.createdAt) }}
          </el-descriptions-item>
        </el-descriptions>

        <!-- 相关照片笔记 -->
        <div class="related-notes">
          <h4>相关照片笔记</h4>
          <el-button
            type="primary"
            size="small"
            @click="viewRelatedNotes(currentTag.tagName)"
          >
            查看所有相关笔记
          </el-button>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 合并标签弹窗 -->
    <el-dialog
      v-model="mergeDialogVisible"
      title="合并标签"
      width="500px"
    >
      <el-form :model="mergeForm" label-width="100px">
        <el-form-item label="源标签">
          <el-select
            v-model="mergeForm.sourceTag"
            placeholder="请选择要合并的源标签"
            style="width: 100%"
          >
            <el-option
              v-for="tag in selectedRows"
              :key="tag.tagName"
              :label="`#${tag.tagName}#`"
              :value="tag.tagName"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="目标标签">
          <el-input
            v-model="mergeForm.targetTag"
            placeholder="请输入目标标签名称"
          />
        </el-form-item>

        <el-alert
          title="注意：合并后源标签将被删除，所有相关数据将转移到目标标签"
          type="warning"
          show-icon
          :closable="false"
        />
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="mergeDialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="confirmMerge"
            :disabled="!mergeForm.sourceTag || !mergeForm.targetTag"
          >
            确认合并
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Refresh, Search } from '@element-plus/icons-vue'
import { getTagList, getTagStats, deleteTag as deleteTagApi, mergeTags } from '@/api/admin'
import { formatDateTime } from '@/utils/date'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const selectedRows = ref([])
const detailDialogVisible = ref(false)
const mergeDialogVisible = ref(false)
const currentTag = ref(null)

// 搜索表单
const searchForm = reactive({
  tagName: '',
  sortBy: 'hotScore',
  minUseCount: null
})

// 分页信息
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 合并表单
const mergeForm = reactive({
  sourceTag: '',
  targetTag: ''
})

// 方法
const loadData = async () => {
  try {
    loading.value = true
    const params = {
      page: pagination.page,
      size: pagination.size,
      tagName: searchForm.tagName || undefined,
      sortBy: searchForm.sortBy,
      minUseCount: searchForm.minUseCount
    }

    const response = await getTagList(params)
    tableData.value = response.data.records
    pagination.total = response.data.total
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.page = 1
  loadData()
}

const resetSearch = () => {
  Object.assign(searchForm, {
    tagName: '',
    sortBy: 'hotScore',
    minUseCount: null
  })
  pagination.page = 1
  loadData()
}

const refreshData = () => {
  loadData()
}

const handleSelectionChange = (selection) => {
  selectedRows.value = selection
}

const handleSizeChange = (size) => {
  pagination.size = size
  pagination.page = 1
  loadData()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  loadData()
}

const viewTagDetail = async (tag) => {
  try {
    const response = await getTagStats(tag.tagName)
    currentTag.value = response.data
    detailDialogVisible.value = true
  } catch (error) {
    console.error('获取标签详情失败:', error)
    ElMessage.error('获取标签详情失败')
  }
}

const viewRelatedNotes = (tagName) => {
  // 跳转到照片笔记管理页面，并筛选该标签
  router.push({
    path: '/content/photo-notes',
    query: { tag: tagName }
  })
}

const deleteTag = async (tag) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除标签 "#${tag.tagName}#" 吗？删除后无法恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await deleteTagApi(tag.tagName)
    ElMessage.success('删除成功')
    
    // 重新加载数据
    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

const batchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedRows.value.length} 个标签吗？删除后无法恢复。`,
      '确认批量删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 批量删除
    for (const tag of selectedRows.value) {
      await deleteTagApi(tag.tagName)
    }

    ElMessage.success('批量删除成功')
    selectedRows.value = []
    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败')
    }
  }
}

const showMergeDialog = (tags = null) => {
  if (tags) {
    selectedRows.value = tags
  }
  
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请先选择要合并的标签')
    return
  }

  mergeForm.sourceTag = selectedRows.value[0]?.tagName || ''
  mergeForm.targetTag = ''
  mergeDialogVisible.value = true
}

const confirmMerge = async () => {
  try {
    await mergeTags(mergeForm.sourceTag, mergeForm.targetTag)
    ElMessage.success('标签合并成功')
    
    mergeDialogVisible.value = false
    selectedRows.value = []
    loadData()
  } catch (error) {
    console.error('标签合并失败:', error)
    ElMessage.error('标签合并失败')
  }
}

const getHotScoreType = (score) => {
  if (score >= 100) return 'danger'
  if (score >= 50) return 'warning'
  if (score >= 10) return 'success'
  return 'info'
}

// 生命周期
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.tag-management {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-section {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.batch-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f0f9ff;
  border-radius: 4px;
}

.batch-buttons {
  display: flex;
  gap: 10px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.tag-detail {
  max-height: 600px;
  overflow-y: auto;
}

.related-notes {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.related-notes h4 {
  margin-bottom: 10px;
}
</style>
