import request from '@/utils/request'

/**
 * 获取照片笔记列表（管理端）
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getPhotoNoteList(params) {
  return request({
    url: '/api/admin/photo-notes/list',
    method: 'get',
    params
  })
}

/**
 * 获取照片笔记详情（管理端）
 * @param {number} noteId 照片笔记ID
 * @returns {Promise}
 */
export function getPhotoNoteDetail(noteId) {
  return request({
    url: `/api/admin/photo-notes/${noteId}`,
    method: 'get'
  })
}

/**
 * 审核照片笔记
 * @param {number} noteId 照片笔记ID
 * @param {number} status 审核状态：1-通过，2-拒绝
 * @param {string} rejectReason 拒绝原因（状态为拒绝时必填）
 * @returns {Promise}
 */
export function auditPhotoNote(noteId, status, rejectReason = null) {
  return request({
    url: `/api/admin/photo-notes/${noteId}/audit`,
    method: 'post',
    params: {
      status,
      rejectReason
    }
  })
}

/**
 * 删除照片笔记（管理端）
 * @param {number} noteId 照片笔记ID
 * @returns {Promise}
 */
export function deletePhotoNote(noteId) {
  return request({
    url: `/api/admin/photo-notes/${noteId}`,
    method: 'delete'
  })
}

/**
 * 获取待审核照片笔记
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getPendingPhotoNotes(params) {
  return request({
    url: '/api/admin/photo-notes/pending',
    method: 'get',
    params
  })
}

/**
 * 获取审核拒绝的照片笔记
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getRejectedPhotoNotes(params) {
  return request({
    url: '/api/admin/photo-notes/rejected',
    method: 'get',
    params
  })
}

/**
 * 获取照片笔记统计信息
 * @returns {Promise}
 */
export function getPhotoNoteStats() {
  return request({
    url: '/api/admin/photo-notes/stats',
    method: 'get'
  })
}

/**
 * 获取标签管理列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getTagList(params) {
  return request({
    url: '/api/admin/tags/list',
    method: 'get',
    params
  })
}

/**
 * 获取标签统计信息
 * @param {string} tagName 标签名称
 * @returns {Promise}
 */
export function getTagStats(tagName) {
  return request({
    url: `/api/admin/tags/${encodeURIComponent(tagName)}/stats`,
    method: 'get'
  })
}

/**
 * 删除标签
 * @param {string} tagName 标签名称
 * @returns {Promise}
 */
export function deleteTag(tagName) {
  return request({
    url: `/api/admin/tags/${encodeURIComponent(tagName)}`,
    method: 'delete'
  })
}

/**
 * 合并标签
 * @param {string} sourceTag 源标签
 * @param {string} targetTag 目标标签
 * @returns {Promise}
 */
export function mergeTags(sourceTag, targetTag) {
  return request({
    url: '/api/admin/tags/merge',
    method: 'post',
    data: {
      sourceTag,
      targetTag
    }
  })
}

/**
 * 获取用户管理列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getUserList(params) {
  return request({
    url: '/api/admin/users/list',
    method: 'get',
    params
  })
}

/**
 * 获取用户详情
 * @param {number} userId 用户ID
 * @returns {Promise}
 */
export function getUserDetail(userId) {
  return request({
    url: `/api/admin/users/${userId}`,
    method: 'get'
  })
}

/**
 * 更新用户状态
 * @param {number} userId 用户ID
 * @param {number} status 用户状态
 * @returns {Promise}
 */
export function updateUserStatus(userId, status) {
  return request({
    url: `/api/admin/users/${userId}/status`,
    method: 'put',
    data: { status }
  })
}

/**
 * 获取系统统计信息
 * @returns {Promise}
 */
export function getSystemStats() {
  return request({
    url: '/api/admin/stats/overview',
    method: 'get'
  })
}

/**
 * 获取内容审核统计
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getContentAuditStats(params) {
  return request({
    url: '/api/admin/stats/content-audit',
    method: 'get',
    params
  })
}

/**
 * 获取用户活跃度统计
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getUserActivityStats(params) {
  return request({
    url: '/api/admin/stats/user-activity',
    method: 'get',
    params
  })
}
