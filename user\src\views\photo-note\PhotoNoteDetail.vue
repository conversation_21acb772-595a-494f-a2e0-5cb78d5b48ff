<template>
  <div class="photo-note-detail">
    <!-- 顶部导航栏 -->
    <van-nav-bar
      title="照片笔记"
      left-arrow
      @click-left="$router.back()"
      class="detail-navbar"
    >
      <template #right>
        <van-icon name="share-o" @click="shareNote" />
      </template>
    </van-nav-bar>

    <!-- 加载状态 -->
    <van-loading v-if="loading" type="spinner" color="#1989fa" vertical class="loading-container">
      加载中...
    </van-loading>

    <!-- 照片笔记内容 -->
    <div v-else-if="noteDetail" class="note-content">
      <!-- 用户信息 -->
      <div class="user-info">
        <van-image
          :src="noteDetail.avatar"
          round
          width="40"
          height="40"
          fit="cover"
          class="user-avatar"
          @click="goToUserProfile"
        />
        <div class="user-details">
          <div class="user-name" @click="goToUserProfile">{{ noteDetail.nickname }}</div>
          <div class="publish-time">{{ formatTime(noteDetail.createdAt) }}</div>
        </div>
        <van-button
          v-if="!isOwnNote"
          type="primary"
          size="small"
          round
          @click="toggleFollow"
        >
          {{ isFollowing ? '已关注' : '关注' }}
        </van-button>
      </div>

      <!-- 照片展示 -->
      <div class="photo-section">
        <div class="photo-grid" :class="getPhotoGridClass()">
          <div
            v-for="(image, index) in noteDetail.images"
            :key="image.photoId"
            class="photo-item"
            @click="previewPhoto(index)"
          >
            <van-image
              :src="privateImageUrls[index] || image.thumbnailUrl || image.url"
              fit="cover"
              width="100%"
              height="100%"
              :alt="`照片${index + 1}`"
            >
              <template #loading>
                <van-loading type="spinner" size="20" />
              </template>
              <template #error>
                <div class="error-placeholder">
                  <van-icon name="photo-fail" size="24" />
                </div>
              </template>
            </van-image>
          </div>
        </div>
      </div>

      <!-- 标题和内容 -->
      <div class="content-section">
        <h2 v-if="noteDetail.title" class="note-title">{{ noteDetail.title }}</h2>
        <div class="note-content-text" v-html="noteDetail.processedContent"></div>
      </div>

      <!-- 位置信息 -->
      <div v-if="noteDetail.location" class="location-section">
        <van-icon name="location-o" />
        <span>{{ noteDetail.location }}</span>
      </div>

      <!-- 互动数据 -->
      <div class="stats-section">
        <div class="stats-item">
          <van-icon name="eye-o" />
          <span>{{ noteDetail.viewCount }}</span>
        </div>
        <div class="stats-item">
          <van-icon name="good-job-o" />
          <span>{{ noteDetail.likeCount }}</span>
        </div>
        <div class="stats-item">
          <van-icon name="chat-o" />
          <span>{{ noteDetail.commentCount }}</span>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="action-section">
        <van-button
          :icon="noteDetail.isLiked ? 'good-job' : 'good-job-o'"
          :type="noteDetail.isLiked ? 'danger' : 'default'"
          size="large"
          @click="toggleLike"
          :loading="likeLoading"
        >
          {{ noteDetail.isLiked ? '已点赞' : '点赞' }}
        </van-button>

        <van-button
          :icon="noteDetail.isCollected ? 'star' : 'star-o'"
          :type="noteDetail.isCollected ? 'warning' : 'default'"
          size="large"
          @click="toggleCollect"
          :loading="collectLoading"
        >
          {{ noteDetail.isCollected ? '已收藏' : '收藏' }}
        </van-button>

        <van-button
          icon="chat-o"
          size="large"
          @click="showComments = true"
        >
          评论
        </van-button>
      </div>

      <!-- 相关推荐 -->
      <div class="related-section">
        <h3>相关推荐</h3>
        <div class="related-notes">
          <!-- TODO: 实现相关推荐 -->
        </div>
      </div>
    </div>

    <!-- 错误状态 -->
    <van-empty v-else description="照片笔记不存在或已被删除" />

    <!-- 照片预览 -->
    <van-image-preview
      v-model:show="showPreview"
      :images="previewImages"
      :start-position="previewIndex"
      @change="onPreviewChange"
    />

    <!-- 评论弹窗 -->
    <van-popup
      v-model:show="showComments"
      position="bottom"
      :style="{ height: '70%' }"
      round
    >
      <div class="comments-container">
        <div class="comments-header">
          <h3>评论 ({{ noteDetail?.commentCount || 0 }})</h3>
          <van-icon name="cross" @click="showComments = false" />
        </div>

        <!-- 评论列表 -->
        <div class="comments-list">
          <!-- TODO: 实现评论列表 -->
        </div>

        <!-- 评论输入 -->
        <div class="comment-input">
          <van-field
            v-model="commentText"
            placeholder="写下你的评论..."
            type="textarea"
            rows="2"
            maxlength="500"
          />
          <van-button
            type="primary"
            size="small"
            @click="submitComment"
            :disabled="!commentText.trim()"
          >
            发送
          </van-button>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { showToast, showShareSheet } from 'vant'
import { getPhotoNoteDetail, likePhotoNote, unlikePhotoNote, collectPhotoNote, uncollectPhotoNote } from '@/api/photo'
import { getPrivateImageUrl } from '@/api/file'
import { formatRelativeTime } from '@/utils/time'

const route = useRoute()
const router = useRouter()

// 响应式数据
const noteDetail = ref(null)
const loading = ref(true)
const showPreview = ref(false)
const previewIndex = ref(0)
const showComments = ref(false)
const commentText = ref('')
const likeLoading = ref(false)
const collectLoading = ref(false)
const isFollowing = ref(false)
const privateImageUrls = ref([])

// 计算属性
const isOwnNote = computed(() => {
  // TODO: 检查是否是自己的笔记
  return false
})

const previewImages = computed(() => {
  if (!noteDetail.value?.images) return []
  return noteDetail.value.images.map((image, index) =>
    privateImageUrls.value[index] || image.url
  )
})

const getPhotoGridClass = computed(() => {
  const count = noteDetail.value?.images?.length || 0
  if (count === 1) return 'grid-1'
  if (count <= 4) return 'grid-2x2'
  return 'grid-3x3'
})

// 方法
const loadNoteDetail = async () => {
  try {
    loading.value = true
    const noteId = route.params.id
    console.log('开始加载照片笔记详情，ID:', noteId)

    const response = await getPhotoNoteDetail(noteId)
    console.log('照片笔记详情API响应:', response)

    noteDetail.value = response.data
    console.log('设置noteDetail.value:', noteDetail.value)
    console.log('图片数据:', noteDetail.value?.images)

    // 处理私有图片URL
    if (noteDetail.value?.images?.length > 0) {
      console.log('开始处理私有图片URL，图片数量:', noteDetail.value.images.length)
      const urls = await Promise.all(
        noteDetail.value.images.map(async (image, index) => {
          try {
            console.log(`处理第${index + 1}张图片:`, image)
            const originalUrl = image.thumbnailUrl || image.url
            console.log(`原始URL:`, originalUrl)
            const processedUrl = await getPrivateImageUrl(originalUrl)
            console.log(`处理后URL:`, processedUrl)
            return processedUrl
          } catch (error) {
            console.error('获取私有图片URL失败:', error)
            return image.thumbnailUrl || image.url
          }
        })
      )
      privateImageUrls.value = urls
      console.log('所有私有图片URL处理完成:', privateImageUrls.value)
    } else {
      console.log('没有图片数据或图片数组为空')
    }
  } catch (error) {
    console.error('加载照片笔记详情失败:', error)
    showToast('加载失败')
  } finally {
    loading.value = false
  }
}

const previewPhoto = (index) => {
  previewIndex.value = index
  showPreview.value = true
}

const onPreviewChange = (index) => {
  previewIndex.value = index
}

const toggleLike = async () => {
  if (likeLoading.value) return

  likeLoading.value = true
  try {
    if (noteDetail.value.isLiked) {
      await unlikePhotoNote(noteDetail.value.id)
      noteDetail.value.isLiked = false
      noteDetail.value.likeCount--
      showToast('取消点赞')
    } else {
      await likePhotoNote(noteDetail.value.id)
      noteDetail.value.isLiked = true
      noteDetail.value.likeCount++
      showToast('点赞成功')
    }
  } catch (error) {
    console.error('点赞操作失败:', error)
    showToast('操作失败')
  } finally {
    likeLoading.value = false
  }
}

const toggleCollect = async () => {
  if (collectLoading.value) return

  collectLoading.value = true
  try {
    if (noteDetail.value.isCollected) {
      await uncollectPhotoNote(noteDetail.value.id)
      noteDetail.value.isCollected = false
      showToast('取消收藏')
    } else {
      await collectPhotoNote(noteDetail.value.id)
      noteDetail.value.isCollected = true
      showToast('收藏成功')
    }
  } catch (error) {
    console.error('收藏操作失败:', error)
    showToast('操作失败')
  } finally {
    collectLoading.value = false
  }
}

const toggleFollow = () => {
  // TODO: 实现关注/取消关注功能
  isFollowing.value = !isFollowing.value
  showToast(isFollowing.value ? '关注成功' : '取消关注')
}

const goToUserProfile = () => {
  router.push(`/user/${noteDetail.value.userId}`)
}

const shareNote = () => {
  const options = [
    { name: '微信', icon: 'wechat' },
    { name: '微博', icon: 'weibo' },
    { name: '复制链接', icon: 'link' }
  ]

  showShareSheet({
    options,
    onSelect: (option) => {
      showToast(`分享到${option.name}`)
    }
  })
}

const submitComment = () => {
  // TODO: 实现评论提交功能
  showToast('评论功能开发中')
  commentText.value = ''
}

const formatTime = (time) => {
  return formatRelativeTime(new Date(time))
}

// 生命周期
onMounted(() => {
  console.log('PhotoNoteDetail组件已挂载')
  console.log('路由参数:', route.params)
  console.log('当前路径:', route.path)
  loadNoteDetail()
})
</script>

<style scoped>
.photo-note-detail {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.detail-navbar {
  background-color: #fff;
  border-bottom: 1px solid #ebedf0;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.note-content {
  background-color: #fff;
  margin: 16px;
  border-radius: 12px;
  overflow: hidden;
}

.user-info {
  display: flex;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.user-avatar {
  margin-right: 12px;
  cursor: pointer;
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  cursor: pointer;
}

.publish-time {
  font-size: 12px;
  color: #999;
  margin-top: 2px;
}

.photo-section {
  padding: 0 16px;
}

.photo-grid {
  display: grid;
  gap: 4px;
  margin-bottom: 16px;
}

.grid-1 {
  grid-template-columns: 1fr;
}

.grid-2x2 {
  grid-template-columns: repeat(2, 1fr);
}

.grid-3x3 {
  grid-template-columns: repeat(3, 1fr);
}

.photo-item {
  aspect-ratio: 1;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
}

.content-section {
  padding: 0 16px 16px;
}

.note-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
}

.note-content-text {
  font-size: 14px;
  line-height: 1.6;
  color: #333;
}

.note-content-text :deep(.tag) {
  color: #1989fa;
  cursor: pointer;
}

.note-content-text :deep(.mention) {
  color: #ff6a00;
  cursor: pointer;
}

.location-section {
  display: flex;
  align-items: center;
  padding: 0 16px 16px;
  font-size: 14px;
  color: #666;
}

.location-section .van-icon {
  margin-right: 4px;
}

.stats-section {
  display: flex;
  padding: 12px 16px;
  border-top: 1px solid #f0f0f0;
  border-bottom: 1px solid #f0f0f0;
}

.stats-item {
  display: flex;
  align-items: center;
  margin-right: 24px;
  font-size: 14px;
  color: #666;
}

.stats-item .van-icon {
  margin-right: 4px;
}

.action-section {
  display: flex;
  gap: 12px;
  padding: 16px;
}

.action-section .van-button {
  flex: 1;
}

.related-section {
  margin-top: 16px;
  padding: 16px;
}

.related-section h3 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 12px;
}

.comments-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.comments-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.comments-header h3 {
  font-size: 16px;
  font-weight: 600;
}

.comments-list {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.comment-input {
  display: flex;
  gap: 12px;
  padding: 16px;
  border-top: 1px solid #f0f0f0;
  align-items: flex-end;
}

.comment-input .van-field {
  flex: 1;
}

.error-placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background-color: #f5f5f5;
  color: #999;
}
</style>
