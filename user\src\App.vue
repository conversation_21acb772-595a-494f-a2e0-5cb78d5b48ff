<template>
  <div class="app-container">
    <!-- 头部导航 -->
    <AppHeader :show-nav-bar="showNavBar" />

    <!-- 主要内容区域 -->
    <div class="main-content" :class="{ 'has-tabbar': showTabBar, 'has-desktop-navbar': !isMobile }">
      <router-view v-slot="{ Component }">
        <keep-alive :include="cachedViews">
          <component :is="Component" />
        </keep-alive>
      </router-view>
    </div>

    <!-- 底部导航 -->
    <AppFooter :show-tab-bar="showTabBar" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useRoute } from 'vue-router'
import { useUserStore } from '@/stores/user'
import websocketService from '@/services/websocket'
import AppHeader from '@/components/layout/AppHeader.vue'
import AppFooter from '@/components/layout/AppFooter.vue'

const route = useRoute()
const userStore = useUserStore()

// 缓存的视图
const cachedViews = ref(['Home', 'Explore', 'Profile'])

// 监听用户登录状态变化
watch(() => userStore.isLoggedIn, (newValue) => {
  if (newValue) {
    // 用户登录后，连接WebSocket
    websocketService.connectNotification()
    // 获取未读通知数量
    userStore.fetchUnreadCount()
  } else {
    // 用户登出后，断开WebSocket连接
    websocketService.disconnect()
  }
})

// 定时获取未读通知数量
let unreadCountTimer: number | null = null

// 自动登录函数
const autoLogin = async () => {
  console.log('尝试自动登录')

  // 从localStorage获取token和用户信息
  const localToken = localStorage.getItem('token')
  const localUserInfo = localStorage.getItem('userInfo')

  if (!localToken) {
    console.log('localStorage中没有token，无法自动登录')
    return false
  }

  console.log('从localStorage恢复token:', localToken)

  // 设置token到store
  userStore.setToken(localToken)

  // 如果有用户信息，从localStorage恢复并直接使用
  if (localUserInfo) {
    try {
      const userInfoObj = JSON.parse(localUserInfo)
      console.log('从localStorage恢复用户信息:', userInfoObj)
      userStore.setUser(userInfoObj)

      // 已经有用户信息，不需要从服务器获取
      console.log('已从localStorage恢复用户信息，不再请求服务器')
      return true
    } catch (e) {
      console.error('解析localStorage中的用户信息失败:', e)
    }
  }

  // 只有在没有本地用户信息时，才尝试从服务器获取
  if (!localUserInfo) {
    try {
      console.log('本地没有用户信息，尝试从服务器获取')
      await userStore.fetchUserInfo()
      return true
    } catch (error) {
      console.error('从服务器获取用户信息失败:', error)
      // 如果获取用户信息失败，清除登录状态
      userStore.resetState()
      return false
    }
  }

  return true
}

onMounted(async () => {
  console.log('App组件挂载，检查登录状态:', userStore.isLoggedIn)

  // 检查是否已经有用户信息
  const storedUserInfo = localStorage.getItem('userInfo')
  const storedToken = localStorage.getItem('token')

  console.log('localStorage中的token:', storedToken)
  console.log('localStorage中的userInfo:', storedUserInfo)

  if (storedToken && storedUserInfo) {
    console.log('已有token和用户信息，直接恢复登录状态')
    try {
      // 解析用户信息
      const userInfoObj = JSON.parse(storedUserInfo)

      // 设置token和用户信息
      userStore.setToken(storedToken)
      userStore.setUser(userInfoObj)

      // 确保isLoggedIn为true
      userStore.setLoggedIn(true)

      // 初始化WebSocket和通知
      console.log('用户已登录，初始化WebSocket和通知')
      websocketService.connectNotification()

      // 获取未读通知数量
      userStore.fetchUnreadCount()

      // 每分钟更新一次未读通知
      unreadCountTimer = window.setInterval(() => {
        if (userStore.isLoggedIn) {
          userStore.fetchUnreadCount()
        }
      }, 60000)

      return
    } catch (e) {
      console.error('解析localStorage中的用户信息失败:', e)
    }
  }

  // 如果没有用户信息或解析失败，尝试自动登录
  console.log('尝试自动登录')
  const loginSuccess = await autoLogin()

  // 如果用户已登录，初始化相关功能
  if (loginSuccess && userStore.isLoggedIn) {
    console.log('用户已登录，初始化WebSocket和通知')
    // 连接WebSocket
    websocketService.connectNotification()

    // 获取未读通知数量
    userStore.fetchUnreadCount()

    // 每分钟更新一次未读通知
    unreadCountTimer = window.setInterval(() => {
      if (userStore.isLoggedIn) {
        userStore.fetchUnreadCount()
      }
    }, 60000)
  } else {
    console.log('用户未登录，跳过初始化WebSocket和通知')
  }
})

onUnmounted(() => {
  // 清除定时器
  if (unreadCountTimer) {
    clearInterval(unreadCountTimer)
  }

  // 断开WebSocket连接
  websocketService.disconnect()
})

// 是否为移动设备
const isMobile = ref(false)

// 检测设备类型
const checkDeviceType = () => {
  isMobile.value = window.innerWidth < 768
}

// 是否显示导航栏
const showNavBar = computed(() => {
  return route.meta.showNavBar !== false
})

// 是否显示底部标签栏
const showTabBar = computed(() => {
  // 在移动端始终显示底部标签栏，除非明确指定不显示
  if (isMobile.value && route.meta.hideTabBar !== true) {
    return true
  }
  // 在桌面端，只在特定页面显示
  return ['/', '/explore', '/notifications', '/user/profile'].includes(route.path)
})



// 监听窗口大小变化
onMounted(() => {
  checkDeviceType()
  window.addEventListener('resize', checkDeviceType)
})

// 组件卸载时移除事件监听
onUnmounted(() => {
  window.removeEventListener('resize', checkDeviceType)
})

// 监听路由变化，更新导航栏标题
watch(() => route.path, () => {
  // 可以在这里添加路由变化时的逻辑
})
</script>

<style lang="scss">
/* 全局样式 */
html, body {
  margin: 0;
  padding: 0;
  font-family: 'PingFang SC', 'Helvetica Neue', Helvetica, 'Hiragino Sans GB', 'Microsoft YaHei', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f7f8fa;
  color: #333;
  height: 100%;
  width: 100%;
}

#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}
.app-container {
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;

  &.has-tabbar {
    padding-bottom: 50px;
  }
}

// 自定义Vant组件样式
:root {
  --van-primary-color: #3498db;
  --van-success-color: #2ecc71;
  --van-danger-color: #e74c3c;
  --van-warning-color: #f39c12;
  --van-text-color: #333;
  --van-active-color: #f2f3f5;
  --van-active-opacity: 0.7;
  --van-disabled-opacity: 0.5;
  --van-background-color: #f5f7fa;
  --van-background-color-light: #fafafa;
  --van-text-link-color: #3498db;
}

// 桌面版导航栏
.desktop-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 60px;
  background-color: #fff;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  z-index: 100;

  .navbar-container {
    max-width: 1200px;
    height: 100%;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .navbar-logo {
      font-size: 20px;
      font-weight: bold;

      a {
        color: var(--van-primary-color);
        text-decoration: none;
      }
    }

    .navbar-links {
      display: flex;
      gap: 24px;

      .navbar-link {
        color: var(--van-text-color);
        text-decoration: none;
        font-size: 16px;
        padding: 8px 0;
        position: relative;

        &.active, &:hover {
          color: var(--van-primary-color);

          &:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 2px;
            background-color: var(--van-primary-color);
          }
        }
      }
    }

    .navbar-actions {
      display: flex;
      align-items: center;
      gap: 16px;

      .navbar-button {
        padding: 8px 16px;
        border-radius: 4px;
        text-decoration: none;
        font-size: 14px;
        white-space: nowrap;
        display: inline-block;
        text-align: center;

        &.upload-button {
          background-color: var(--van-primary-color);
          color: white;
        }

        &.login-button {
          background-color: var(--van-primary-color);
          color: white;
          font-weight: 500;
        }

        &.register-button {
          background-color: var(--van-primary-color);
          color: white;
        }
      }

      .navbar-icon {
        color: var(--van-text-color);
        text-decoration: none;

        &:hover {
          color: var(--van-primary-color);
        }
      }

      .navbar-avatar {
        display: block;
      }
    }
  }
}

// 响应式布局
@media (min-width: 768px) {
  .main-content {
    width: 100%;
    margin: 0 auto;
    padding: 20px;
    min-height: calc(100vh - 60px);

    &.has-tabbar {
      padding-bottom: 20px;
    }

    &.has-desktop-navbar {
      padding-top: 80px;
    }
  }

  .app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
  }
}
</style>
