<template>
  <div class="photo-note-card">
    <!-- 用户信息头部 -->
    <div class="card-header">
      <div class="user-info" @click="goToUserProfile">
        <img :src="note.avatar" :alt="note.nickname" class="user-avatar">
        <div class="user-details">
          <div class="user-name">
            {{ note.nickname }}
            <van-icon v-if="isVerifiedUser" name="passed" class="verified-icon" />
          </div>
          <div class="note-time">{{ formatTime(note.createdAt) }}</div>
        </div>
      </div>
      <div class="header-actions">
        <van-popover v-model="showPopover" placement="bottom-end">
          <template #reference>
            <van-icon name="ellipsis" class="more-icon" />
          </template>
          <div class="action-menu">
            <div class="menu-item" @click="handleReport">举报</div>
            <div class="menu-item" @click="handleBlock">屏蔽用户</div>
          </div>
        </van-popover>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="card-content" @click="goToDetail">
      <div v-if="note.title" class="note-title">{{ note.title }}</div>
      <div class="note-content" v-html="note.processedContent"></div>

      <!-- 照片展示区域 -->
      <div class="photo-grid" :class="getGridClass(note.images.length)">
        <div
          v-for="(photo, index) in note.images"
          :key="photo.photoId"
          class="photo-item"
          @click.stop="previewImage(index)"
        >
          <van-image
            :src="photo.thumbnailUrl"
            :alt="`照片${index + 1}`"
            class="photo-image"
            fit="cover"
            loading="lazy"
          >
            <template #loading>
              <van-loading type="spinner" size="20" />
            </template>
            <template #error>
              <div class="error-placeholder">
                <van-icon name="photo-fail" size="24" />
              </div>
            </template>
          </van-image>
          <div v-if="note.images.length > 1" class="photo-index">{{ index + 1 }}</div>
        </div>
      </div>
    </div>

    <!-- 互动统计 -->
    <div class="card-stats">
      <div class="stats-info">
        <span class="stat-item">
          <van-icon name="good-job-o" />
          {{ formatCount(note.likeCount) }}
        </span>
        <span class="stat-item">
          <van-icon name="chat-o" />
          {{ formatCount(note.commentCount) }}
        </span>
        <span class="stat-item">
          <van-icon name="eye-o" />
          {{ formatCount(note.viewCount) }}
        </span>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="card-actions">
      <van-button
        :icon="note.isLiked ? 'good-job' : 'good-job-o'"
        :class="{ liked: note.isLiked }"
        :loading="likeLoading"
        @click="toggleLike"
        size="small"
        plain
      >
        {{ note.isLiked ? '已赞' : '点赞' }}
      </van-button>

      <van-button
        icon="chat-o"
        @click="goToDetail"
        size="small"
        plain
      >
        评论
      </van-button>

      <van-button
        :icon="note.isCollected ? 'star' : 'star-o'"
        :class="{ collected: note.isCollected }"
        :loading="collectLoading"
        @click="toggleCollect"
        size="small"
        plain
      >
        {{ note.isCollected ? '已收藏' : '收藏' }}
      </van-button>

      <van-button
        icon="share-o"
        @click="handleShare"
        size="small"
        plain
      >
        分享
      </van-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { showImagePreview, showToast, showDialog } from 'vant'
import { likePhotoNote, unlikePhotoNote, collectPhotoNote, uncollectPhotoNote, reportPhotoNote } from '@/api/home'
import type { HomePhotoNote } from '@/api/home'

// 时间格式化函数
const formatTime = (dateString: string): string => {
  const date = new Date(dateString)
  const now = new Date()
  const diff = now.getTime() - date.getTime()

  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))

  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  if (days < 7) return `${days}天前`

  return date.toLocaleDateString('zh-CN', {
    month: 'short',
    day: 'numeric'
  })
}

// Props
interface Props {
  note: HomePhotoNote
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  like: [noteId: number, isLiked: boolean, likeCount: number]
  collect: [noteId: number, isCollected: boolean, collectCount: number]
  comment: [noteId: number]
}>()

// Router
const router = useRouter()

// 响应式数据
const showPopover = ref(false)
const likeLoading = ref(false)
const collectLoading = ref(false)

// 计算属性
const noteData = computed(() => props.note)

// 是否为认证用户（可以根据实际业务逻辑调整）
const isVerifiedUser = computed(() => {
  // 这里可以根据用户等级、认证状态等判断
  return false // 暂时返回false，后续可以扩展
})

// 格式化数字
const formatCount = (count: number): string => {
  if (count < 1000) return count.toString()
  if (count < 10000) return (count / 1000).toFixed(1) + 'k'
  return (count / 10000).toFixed(1) + 'w'
}

// 获取照片网格样式类
const getGridClass = (photoCount: number): string => {
  if (photoCount === 1) return 'grid-single'
  if (photoCount === 2) return 'grid-double'
  if (photoCount <= 4) return 'grid-four'
  return 'grid-nine'
}

// 预览图片
const previewImage = (index: number) => {
  const images = noteData.value.images.map((photo: any) => photo.url)
  showImagePreview({
    images,
    startPosition: index,
    closeable: true
  })
}

// 跳转到详情页
const goToDetail = () => {
  router.push(`/photo-note/${noteData.value.id}`)
}

// 跳转到用户主页
const goToUserProfile = () => {
  router.push(`/user/${noteData.value.userId}`)
}

// 点赞/取消点赞
const toggleLike = async () => {
  if (likeLoading.value) return

  likeLoading.value = true
  try {
    const response = noteData.value.isLiked
      ? await unlikePhotoNote(noteData.value.id)
      : await likePhotoNote(noteData.value.id)

    emit('like', noteData.value.id, response.data.isLiked, response.data.likeCount)
  } catch (error) {
    console.error('点赞操作失败:', error)
    showToast('操作失败，请重试')
  } finally {
    likeLoading.value = false
  }
}

// 收藏/取消收藏
const toggleCollect = async () => {
  if (collectLoading.value) return

  collectLoading.value = true
  try {
    const response = noteData.value.isCollected
      ? await uncollectPhotoNote(noteData.value.id)
      : await collectPhotoNote(noteData.value.id)

    emit('collect', noteData.value.id, response.data.isCollected, response.data.collectCount)
  } catch (error) {
    console.error('收藏操作失败:', error)
    showToast('操作失败，请重试')
  } finally {
    collectLoading.value = false
  }
}

// 分享
const handleShare = () => {
  if (navigator.share) {
    navigator.share({
      title: noteData.value.title || '照片笔记',
      text: noteData.value.content,
      url: `${window.location.origin}/photo-note/${noteData.value.id}`
    })
  } else {
    // 复制链接到剪贴板
    const url = `${window.location.origin}/photo-note/${noteData.value.id}`
    navigator.clipboard.writeText(url).then(() => {
      showToast('链接已复制到剪贴板')
    })
  }
}

// 举报
const handleReport = async () => {
  showPopover.value = false

  try {
    await showDialog({
      title: '举报内容',
      message: '确定要举报这条照片笔记吗？',
      confirmButtonText: '举报',
      cancelButtonText: '取消'
    })

    await reportPhotoNote(noteData.value.id, '不当内容')
    showToast('举报成功')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('举报失败:', error)
      showToast('举报失败，请重试')
    }
  }
}

// 屏蔽用户
const handleBlock = async () => {
  showPopover.value = false

  try {
    await showDialog({
      title: '屏蔽用户',
      message: `确定要屏蔽用户 ${noteData.value.nickname} 吗？`,
      confirmButtonText: '屏蔽',
      cancelButtonText: '取消'
    })

    showToast('屏蔽功能开发中...')
  } catch (error) {
    // 用户取消操作，不需要处理
  }
}
</script>

<style scoped lang="scss">
.photo-note-card {
  background: white;
  margin-bottom: 12px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;

  .user-info {
    display: flex;
    align-items: center;
    cursor: pointer;

    .user-avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      margin-right: 12px;
    }

    .user-details {
      .user-name {
        font-size: 14px;
        font-weight: 600;
        color: #333;
        display: flex;
        align-items: center;

        .verified-icon {
          margin-left: 4px;
          color: #1989fa;
        }
      }

      .note-time {
        font-size: 12px;
        color: #999;
        margin-top: 2px;
      }
    }
  }

  .header-actions {
    .more-icon {
      font-size: 18px;
      color: #999;
      cursor: pointer;
    }
  }
}

.action-menu {
  .menu-item {
    padding: 12px 16px;
    font-size: 14px;
    cursor: pointer;

    &:hover {
      background: #f7f8fa;
    }
  }
}

.card-content {
  padding: 0 16px;
  cursor: pointer;

  .note-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
  }

  .note-content {
    font-size: 14px;
    color: #666;
    line-height: 1.5;
    margin-bottom: 12px;

    :deep(.tag) {
      color: #1989fa;
      cursor: pointer;

      &:hover {
        text-decoration: underline;
      }
    }

    :deep(.mention) {
      color: #ff976a;
      cursor: pointer;

      &:hover {
        text-decoration: underline;
      }
    }
  }
}

.photo-grid {
  display: grid;
  gap: 4px;
  margin-bottom: 12px;

  &.grid-single {
    grid-template-columns: 1fr;
    max-width: 300px;
  }

  &.grid-double {
    grid-template-columns: 1fr 1fr;
  }

  &.grid-four {
    grid-template-columns: 1fr 1fr;
  }

  &.grid-nine {
    grid-template-columns: 1fr 1fr 1fr;
  }

  .photo-item {
    position: relative;
    aspect-ratio: 1;
    border-radius: 4px;
    overflow: hidden;
    cursor: pointer;

    .photo-image {
      width: 100%;
      height: 100%;
    }

    .photo-index {
      position: absolute;
      top: 4px;
      right: 4px;
      background: rgba(0, 0, 0, 0.6);
      color: white;
      font-size: 10px;
      padding: 2px 6px;
      border-radius: 10px;
    }

    .error-placeholder {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      background: #f7f8fa;
      color: #ddd;
    }
  }
}

.card-stats {
  padding: 0 16px 8px;

  .stats-info {
    display: flex;
    gap: 16px;

    .stat-item {
      display: flex;
      align-items: center;
      font-size: 12px;
      color: #999;

      .van-icon {
        margin-right: 4px;
      }
    }
  }
}

.card-actions {
  display: flex;
  justify-content: space-around;
  padding: 12px 16px;
  border-top: 1px solid #f7f8fa;

  .van-button {
    flex: 1;
    margin: 0 4px;

    &.liked {
      color: #ff976a;
    }

    &.collected {
      color: #ffd21e;
    }
  }
}
</style>
