package com.phototagmoment.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.phototagmoment.common.ApiResponse;
import com.phototagmoment.dto.PhotoDTO;
import com.phototagmoment.dto.TagDTO;
import com.phototagmoment.dto.UserDTO;
import com.phototagmoment.security.SecurityUtil;
import com.phototagmoment.service.SearchService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 搜索控制器
 */
@Slf4j
@RestController
@RequestMapping("/search")
@Tag(name = "搜索接口", description = "搜索照片、用户、标签等")
public class SearchController {

    @Autowired
    private SearchService searchService;

    /**
     * 搜索照片
     */
    @GetMapping("/photos")
    @Operation(summary = "搜索照片", description = "根据关键词搜索照片")
    public ApiResponse<IPage<PhotoDTO>> searchPhotos(
            @Parameter(description = "关键词") @RequestParam String keyword,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "12") Integer size,
            @Parameter(description = "排序方式：recent-最新，popular-热门") @RequestParam(defaultValue = "recent") String sort) {
        Long currentUserId = SecurityUtil.getCurrentUserId();
        IPage<PhotoDTO> photoPage = searchService.searchPhotos(keyword, page, size, sort, currentUserId);
        return ApiResponse.success(photoPage);
    }

    /**
     * 搜索用户
     */
    @GetMapping("/users")
    @Operation(summary = "搜索用户", description = "根据关键词搜索用户")
    public ApiResponse<IPage<UserDTO>> searchUsers(
            @Parameter(description = "关键词") @RequestParam String keyword,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") Integer size) {
        Long currentUserId = SecurityUtil.getCurrentUserId();
        IPage<UserDTO> userPage = searchService.searchUsers(keyword, page, size, currentUserId);
        return ApiResponse.success(userPage);
    }

    /**
     * 搜索标签
     */
    @GetMapping("/tags")
    @Operation(summary = "搜索标签", description = "根据关键词搜索标签")
    public ApiResponse<List<TagDTO>> searchTags(
            @Parameter(description = "关键词") @RequestParam String keyword,
            @Parameter(description = "数量限制") @RequestParam(defaultValue = "20") Integer limit) {
        List<TagDTO> tags = searchService.searchTags(keyword, limit);
        return ApiResponse.success(tags);
    }

    /**
     * 综合搜索
     */
    @GetMapping
    @Operation(summary = "综合搜索", description = "同时搜索照片、用户和标签")
    public ApiResponse<Map<String, Object>> search(
            @Parameter(description = "关键词") @RequestParam String keyword,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") Integer size,
            @Parameter(description = "排序方式：recent-最新，popular-热门") @RequestParam(defaultValue = "recent") String sort,
            @Parameter(description = "搜索类型：all-全部，photos-照片，users-用户，tags-标签") @RequestParam(defaultValue = "all") String type) {
        Long currentUserId = SecurityUtil.getCurrentUserId();
        Map<String, Object> result = searchService.search(keyword, page, size, sort, type, currentUserId);
        return ApiResponse.success(result);
    }

    /**
     * 获取热门标签
     */
    @GetMapping("/popular-tags")
    @Operation(summary = "获取热门标签", description = "获取热门标签列表")
    public ApiResponse<List<TagDTO>> getPopularTags(
            @Parameter(description = "数量限制") @RequestParam(defaultValue = "20") Integer limit) {
        List<TagDTO> tags = searchService.getPopularTags(limit);
        return ApiResponse.success(tags);
    }

    /**
     * 获取推荐用户
     */
    @GetMapping("/recommended-users")
    @Operation(summary = "获取推荐用户", description = "获取推荐用户列表")
    public ApiResponse<List<UserDTO>> getRecommendedUsers(
            @Parameter(description = "数量限制") @RequestParam(defaultValue = "5") Integer limit) {
        Long currentUserId = SecurityUtil.getCurrentUserId();
        List<UserDTO> users = searchService.getRecommendedUsers(limit, currentUserId);
        return ApiResponse.success(users);
    }
}
