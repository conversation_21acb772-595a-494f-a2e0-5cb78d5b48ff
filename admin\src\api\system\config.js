import request from '@/utils/request'

/**
 * 获取所有系统配置
 */
export function getAllSystemConfig() {
  return request({
    url: '/admin/system/config/list',
    method: 'get'
  })
}

/**
 * 分页获取系统配置
 * @param {Object} params 查询参数
 */
export function getSystemConfigPage(params) {
  return request({
    url: '/admin/system/config/page',
    method: 'get',
    params
  })
}

/**
 * 根据ID获取系统配置
 * @param {number} id 配置ID
 */
export function getConfigDetail(id) {
  return request({
    url: `/admin/system/config/${id}`,
    method: 'get'
  })
}

/**
 * 根据配置键获取配置值
 * @param {string} configKey 配置键
 */
export function getConfigValue(configKey) {
  return request({
    url: `/admin/system/config/value/${configKey}`,
    method: 'get'
  })
}

/**
 * 批量获取配置值
 * @param {Array} keys 配置键列表
 */
export function getSystemConfig(keys) {
  return request({
    url: '/admin/system/config/batch',
    method: 'post',
    data: { keys }
  }).then(res => {
    if (res.code === 200) {
      return res.data
    }
    return {}
  })
}

/**
 * 保存系统配置
 * @param {Object} data 配置数据
 */
export function saveSystemConfig(data) {
  return request({
    url: '/admin/system/config',
    method: 'post',
    data
  })
}

/**
 * 批量保存系统配置
 * @param {Object} data 配置键值对
 */
export function updateSystemConfig(data) {
  return request({
    url: '/admin/system/config/batch',
    method: 'put',
    data
  })
}

/**
 * 更新系统配置
 * @param {Object} data 配置数据
 */
export function updateConfig(data) {
  return request({
    url: '/admin/system/config',
    method: 'put',
    data
  })
}

/**
 * 更新配置值
 * @param {string} configKey 配置键
 * @param {string} configValue 配置值
 */
export function updateConfigValue(configKey, configValue) {
  return request({
    url: '/admin/system/config/value',
    method: 'put',
    params: {
      configKey,
      configValue
    }
  })
}

/**
 * 删除系统配置
 * @param {number} id 配置ID
 */
export function deleteConfig(id) {
  return request({
    url: `/admin/system/config/${id}`,
    method: 'delete'
  })
}

/**
 * 刷新配置缓存
 */
export function refreshConfigCache() {
  return request({
    url: '/admin/system/config/refresh',
    method: 'post'
  })
}
