import request from '@/utils/request'

// 照片列表参数接口
interface PhotoListParams {
  page?: number;
  pageSize?: number;
  keyword?: string;
  status?: number;
  startDate?: string;
  endDate?: string;
  userId?: number;
  categoryId?: number;
  [key: string]: any;
}

// 照片数据接口
interface PhotoData {
  id: number;
  title?: string;
  description?: string;
  status?: number;
  isPublic?: number;
  categoryId?: number;
  tags?: string[];
  [key: string]: any;
}

// 审核数据接口
interface AuditData {
  photoId: number;
  status: number;
  reason?: string;
  [key: string]: any;
}

// 批量审核数据接口
interface BatchAuditData {
  photoIds: number[];
  status: number;
  reason?: string;
  [key: string]: any;
}

/**
 * 获取照片列表
 * @param params 查询参数
 */
export function getPhotoList(params: PhotoListParams) {
  return request({
    url: '/admin/photos',
    method: 'get',
    params
  })
}

/**
 * 获取照片详情
 * @param id 照片ID
 */
export function getPhotoDetail(id: number) {
  return request({
    url: `/admin/photos/${id}`,
    method: 'get'
  })
}

/**
 * 更新照片信息
 * @param data 照片数据
 */
export function updatePhoto(data: PhotoData) {
  return request({
    url: `/admin/photos/${data.id}`,
    method: 'put',
    data
  })
}

/**
 * 删除照片
 * @param id 照片ID
 */
export function deletePhoto(id: number) {
  return request({
    url: `/admin/photos/${id}`,
    method: 'delete'
  })
}

/**
 * 获取待审核照片列表
 * @param params 查询参数
 */
export function getPendingAuditPhotos(params: PhotoListParams) {
  return request({
    url: '/admin/photo-audit/pending',
    method: 'get',
    params
  })
}

/**
 * 审核照片
 * @param data 审核数据
 */
export function auditPhoto(data: AuditData) {
  return request({
    url: '/admin/photo-audit/audit',
    method: 'post',
    params: data
  })
}

/**
 * 批量审核照片
 * @param data 批量审核数据
 */
export function batchAuditPhotos(data: BatchAuditData) {
  return request({
    url: '/admin/photo-audit/batch-audit',
    method: 'post',
    data
  })
}

/**
 * 获取照片审核历史
 * @param photoId 照片ID
 */
export function getPhotoAuditHistory(photoId: number) {
  return request({
    url: `/admin/photo-audit/history/${photoId}`,
    method: 'get'
  })
}
