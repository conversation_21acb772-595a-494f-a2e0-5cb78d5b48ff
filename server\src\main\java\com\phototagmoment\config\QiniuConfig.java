package com.phototagmoment.config;

import com.phototagmoment.service.SystemConfigService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;

/**
 * 七牛云配置类
 */
@Slf4j
@Data
@Configuration
@ConfigurationProperties(prefix = "storage.qiniu")
public class QiniuConfig {

    @Autowired
    private SystemConfigService systemConfigService;

    /**
     * 是否启用七牛云存储
     */
    private boolean enabled = false;

    /**
     * 访问密钥
     */
    private String accessKey;

    /**
     * 密钥
     */
    private String secretKey;

    /**
     * 存储空间名称
     */
    private String bucket;

    /**
     * 存储区域
     */
    private String region;

    /**
     * 访问域名
     */
    private String domain;

    /**
     * 上传目录
     */
    private String uploadDir = "uploads";

    /**
     * 是否启用加密存储
     */
    private boolean encryptEnabled = true;

    /**
     * 是否为私有空间
     */
    private boolean isPrivate = true;

    /**
     * 下载凭证有效期（秒）
     */
    private long downloadExpires = 3600;

    /**
     * 初始化后从数据库加载配置
     */
    @PostConstruct
    public void loadConfigFromDatabase() {
        try {
            refreshConfig();
            log.info("七牛云配置初始化完成，enabled: {}", enabled);
        } catch (Exception e) {
            log.warn("从数据库加载七牛云配置失败，使用默认配置: {}", e.getMessage());
        }
    }

    /**
     * 从数据库刷新配置
     */
    public void refreshConfig() {
        if (systemConfigService == null) {
            log.warn("SystemConfigService未初始化，跳过配置刷新");
            return;
        }

        try {
            // 读取存储类型配置，判断是否启用七牛云
            String storageType = systemConfigService.getConfigValue("storage.type", "local");
            this.enabled = "qiniu".equals(storageType);

            // 读取七牛云具体配置
            this.accessKey = systemConfigService.getConfigValue("storage.qiniu.access-key", "");
            this.secretKey = systemConfigService.getConfigValue("storage.qiniu.secret-key", "");
            this.bucket = systemConfigService.getConfigValue("storage.qiniu.bucket", "");
            this.region = systemConfigService.getConfigValue("storage.qiniu.region", "");
            this.domain = systemConfigService.getConfigValue("storage.qiniu.domain", "");
            this.uploadDir = systemConfigService.getConfigValue("storage.qiniu.upload-dir", "uploads");
            this.isPrivate = systemConfigService.getBooleanValue("storage.qiniu.is-private", true);
            this.downloadExpires = systemConfigService.getLongValue("storage.qiniu.download-expires", 3600L);

            log.debug("七牛云配置刷新完成: enabled={}, bucket={}, domain={}", enabled, bucket, domain);
        } catch (Exception e) {
            log.error("刷新七牛云配置失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 检查配置是否完整
     */
    public boolean isConfigComplete() {
        if (!enabled) {
            return false;
        }

        return StringUtils.hasText(accessKey) &&
               StringUtils.hasText(secretKey) &&
               StringUtils.hasText(bucket) &&
               StringUtils.hasText(region) &&
               StringUtils.hasText(domain);
    }

    /**
     * 获取配置验证错误信息
     */
    public String getConfigValidationError() {
        if (!enabled) {
            return "七牛云存储未启用";
        }

        if (!StringUtils.hasText(accessKey)) {
            return "七牛云AccessKey未配置";
        }

        if (!StringUtils.hasText(secretKey)) {
            return "七牛云SecretKey未配置";
        }

        if (!StringUtils.hasText(bucket)) {
            return "七牛云存储空间未配置";
        }

        if (!StringUtils.hasText(region)) {
            return "七牛云存储区域未配置";
        }

        if (!StringUtils.hasText(domain)) {
            return "七牛云访问域名未配置";
        }

        return null;
    }
}
