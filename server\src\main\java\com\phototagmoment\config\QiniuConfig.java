package com.phototagmoment.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 七牛云配置类
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "storage.qiniu")
public class QiniuConfig {

    /**
     * 是否启用七牛云存储
     */
    private boolean enabled = false;

    /**
     * 访问密钥
     */
    private String accessKey;

    /**
     * 密钥
     */
    private String secretKey;

    /**
     * 存储空间名称
     */
    private String bucket;

    /**
     * 存储区域
     */
    private String region;

    /**
     * 访问域名
     */
    private String domain;

    /**
     * 上传目录
     */
    private String uploadDir = "uploads";

    /**
     * 是否启用加密存储
     */
    private boolean encryptEnabled = true;

    /**
     * 是否为私有空间
     */
    private boolean isPrivate = true;

    /**
     * 下载凭证有效期（秒）
     */
    private long downloadExpires = 3600;
}
