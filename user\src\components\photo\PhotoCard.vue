<template>
  <div class="photo-card" @click="navigateToDetail">
    <div class="photo-container">
      <!-- 使用Vant的Swipe组件实现照片轮播 -->
      <van-swipe
        v-if="hasMultiplePhotos"
        class="photo-swipe"
        :autoplay="3000"
        indicator-color="#fff"
        @change="(index) => current = index"
        @click="previewImage"
      >
        <van-swipe-item v-for="(groupPhoto, index) in groupPhotosWithUrls" :key="index">
          <van-image
            :src="groupPhoto.privateUrl"
            :alt="photo.title"
            fit="cover"
            lazy-load
            class="photo-image"
          >
            <template #loading>
              <van-loading type="spinner" size="20" />
            </template>
            <template #error>
              <div class="error-placeholder">
                <van-icon name="photo-fail" size="24" />
              </div>
            </template>
          </van-image>
        </van-swipe-item>

        <!-- 照片计数指示器 -->
        <template #indicator>
          <div class="custom-indicator">
            {{ current + 1 }}/{{ groupPhotosWithUrls.length }}
          </div>
        </template>
      </van-swipe>

      <!-- 单张照片显示 -->
      <van-image
        v-else
        :src="privateImageUrl"
        :alt="photo.title"
        fit="cover"
        lazy-load
        class="photo-image"
        @click.stop="previewImage"
      >
        <template #loading>
          <van-loading type="spinner" size="20" />
        </template>
        <template #error>
          <div class="error-placeholder">
            <van-icon name="photo-fail" size="24" />
          </div>
        </template>
      </van-image>

      <!-- 照片状态标签 -->
      <div v-if="photo.status !== 1" class="status-badge">
        <PhotoStatus :status="photo.status" />
      </div>

      <div v-if="showOverlay" class="photo-overlay">
        <div class="photo-stats">
          <div class="stat-item">
            <van-icon name="like-o" />
            <span>{{ formatNumber(photo.likeCount) }}</span>
          </div>
          <div class="stat-item">
            <van-icon name="comment-o" />
            <span>{{ formatNumber(photo.commentCount) }}</span>
          </div>
        </div>
      </div>
    </div>

    <div v-if="showInfo" class="photo-info">
      <div class="photo-title">{{ photo.title }}</div>

      <!-- 富文本描述 -->
      <div v-if="photo.description" class="photo-description" v-html="formatDescription(photo.description)"></div>

      <!-- 标签 -->
      <div v-if="photo.tags && photo.tags.length > 0" class="photo-tags">
        <span
          v-for="tag in photo.tags"
          :key="tag"
          class="tag-item"
          @click.stop="handleTagClick(tag)"
        >
          #{{ tag }}
        </span>
      </div>

      <div class="photo-meta">
        <div class="user-info" @click.stop="navigateToUser">
          <van-image
            :src="privateAvatarUrl"
            :alt="getUserNickname"
            round
            width="24"
            height="24"
          />
          <span class="username">{{ getUserNickname }}</span>
        </div>

        <div class="action-buttons">
          <van-icon
            :name="photo.isLiked ? 'like' : 'like-o'"
            :class="{ 'liked': photo.isLiked }"
            @click.stop="toggleLike"
          />
          <van-icon
            name="star-o"
            :class="{ 'collected': photo.isCollected }"
            @click.stop="toggleCollect"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import PhotoStatus from './PhotoStatus.vue'
import { getPrivateImageUrl } from '@/api/file'
import { getPhotosByGroupId } from '@/api/photo'
import { ref, onMounted, computed } from 'vue'
import { showImagePreview } from 'vant'

interface PhotoUser {
  id: number;
  nickname: string;
  avatar: string;
}

interface GroupPhoto {
  id: number;
  url: string;
  thumbnailUrl: string;
  privateUrl?: string;
}

interface Photo {
  id: number;
  title: string;
  description?: string; // 照片描述
  thumbnailUrl: string;
  likeCount: number;
  commentCount: number;
  isLiked: boolean;
  isCollected: boolean;
  user: PhotoUser;
  status: number; // 照片状态: 0-待审核, 1-正常, 2-审核拒绝, 3-已删除
  groupId?: string; // 照片分组ID
  groupPhotoIds?: number[]; // 同组照片ID列表
  isGrouped?: boolean; // 是否是分组照片
  groupPhotoCount?: number; // 同组照片数量
  tags?: string[]; // 标签列表
}

const props = defineProps({
  photo: {
    type: Object as () => Photo,
    required: true
  },
  showOverlay: {
    type: Boolean,
    default: false
  },
  showInfo: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['like', 'collect'])
const router = useRouter()

// 私有图片URL
const privateImageUrl = ref('')
const privateAvatarUrl = ref('')
const groupPhotosWithUrls = ref<GroupPhoto[]>([])
const current = ref(0)

// 是否有多张照片
const hasMultiplePhotos = computed(() => {
  return props.photo.isGrouped &&
         props.photo.groupPhotoIds &&
         props.photo.groupPhotoIds.length > 1;
})

// 获取用户昵称，兼容不同的数据结构
const getUserNickname = computed(() => {
  if (props.photo.user && props.photo.user.nickname) {
    return props.photo.user.nickname;
  } else if (props.photo.user && props.photo.user.username) {
    return props.photo.user.username;
  } else if (props.photo.userNickname) {
    return props.photo.userNickname;
  } else if (props.photo.userName) {
    return props.photo.userName;
  } else {
    return '未知用户';
  }
})

// 获取用户ID，兼容不同的数据结构
const getUserId = computed(() => {
  if (props.photo.user && props.photo.user.id) {
    return props.photo.user.id;
  } else if (props.photo.userId) {
    return props.photo.userId;
  } else {
    return 0;
  }
})

// 获取带下载凭证的图片URL
onMounted(async () => {
  // 获取照片缩略图URL
  if (props.photo.thumbnailUrl) {
    privateImageUrl.value = await getPrivateImageUrl(props.photo.thumbnailUrl)
  } else if (props.photo.url) {
    privateImageUrl.value = await getPrivateImageUrl(props.photo.url)
  }

  // 获取用户头像URL，兼容不同的数据结构
  if (props.photo.user && props.photo.user.avatar) {
    privateAvatarUrl.value = await getPrivateImageUrl(props.photo.user.avatar)
  } else if (props.photo.userAvatar) {
    privateAvatarUrl.value = await getPrivateImageUrl(props.photo.userAvatar)
  } else {
    // 使用默认头像
    privateAvatarUrl.value = 'https://randomuser.me/api/portraits/men/1.jpg'
  }

  // 如果是分组照片，获取同组照片的URL
  if (hasMultiplePhotos.value) {
    await loadGroupPhotosUrls()
  }
})

// 加载同组照片的URL
const loadGroupPhotosUrls = async () => {
  try {
    // 如果后端已经返回了同组照片的信息
    if (props.photo.groupPhotos && props.photo.groupPhotos.length > 0) {
      const photos = [];
      for (const groupPhoto of props.photo.groupPhotos) {
        const privateUrl = await getPrivateImageUrl(groupPhoto.thumbnailUrl || groupPhoto.url);
        photos.push({
          id: groupPhoto.id,
          url: groupPhoto.url,
          thumbnailUrl: groupPhoto.thumbnailUrl,
          privateUrl
        });
      }
      groupPhotosWithUrls.value = photos;
    }
    // 如果后端只返回了同组照片的ID列表，但没有返回完整的照片信息
    else if (props.photo.groupPhotoIds && props.photo.groupPhotoIds.length > 0) {
      // 先添加当前照片
      groupPhotosWithUrls.value = [{
        id: props.photo.id,
        url: props.photo.url,
        thumbnailUrl: props.photo.thumbnailUrl,
        privateUrl: privateImageUrl.value
      }];

      // 如果有分组ID，尝试获取同组照片
      if (props.photo.groupId) {
        try {
          const res = await getPhotosByGroupId(props.photo.groupId);
          if (res && res.code === 200 && res.data && res.data.length > 0) {
            // 清空之前的数据
            groupPhotosWithUrls.value = [];

            // 添加所有照片
            for (const groupPhoto of res.data) {
              let privateUrl = '';
              if (groupPhoto.thumbnailUrl) {
                privateUrl = await getPrivateImageUrl(groupPhoto.thumbnailUrl);
              } else if (groupPhoto.url) {
                privateUrl = await getPrivateImageUrl(groupPhoto.url);
              }

              groupPhotosWithUrls.value.push({
                id: groupPhoto.id,
                url: groupPhoto.url,
                thumbnailUrl: groupPhoto.thumbnailUrl,
                privateUrl
              });
            }
          }
        } catch (error) {
          console.error('获取同组照片失败', error);
        }
      }
    }
  } catch (error) {
    console.error('加载同组照片URL失败', error);
  }
}

// 格式化数字
const formatNumber = (num: number) => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num
}

// 预览照片
const previewImage = (event: Event) => {
  event.stopPropagation(); // 阻止事件冒泡，防止触发navigateToDetail

  // 如果有多张照片
  if (hasMultiplePhotos.value && groupPhotosWithUrls.value.length > 0) {
    const images = groupPhotosWithUrls.value.map(photo => photo.privateUrl || '');
    showImagePreview({
      images,
      startPosition: current.value,
      closeable: true,
      closeIconPosition: 'top-right'
    });
  } else {
    // 单张照片
    showImagePreview({
      images: [privateImageUrl.value],
      closeable: true,
      closeIconPosition: 'top-right'
    });
  }
}

// 导航到照片详情页
const navigateToDetail = () => {
  router.push(`/photo/detail/${props.photo.id}`)
}

// 导航到用户页面
const navigateToUser = () => {
  router.push(`/user/${getUserId.value}`)
}

// 点赞/取消点赞
const toggleLike = () => {
  emit('like', props.photo.id)
}

// 收藏/取消收藏
const toggleCollect = () => {
  emit('collect', props.photo.id)
}

// 格式化描述，将@用户和#标签转换为链接
const formatDescription = (description: string) => {
  if (!description) return '';

  // 替换@用户为链接
  let formattedText = description.replace(/@(\w+)/g, '<a class="mention" href="/user/$1">@$1</a>');

  // 替换#标签为链接
  formattedText = formattedText.replace(/#(\w+)/g, '<a class="hashtag" href="/search?keyword=$1&type=tag">#$1</a>');

  // 替换换行符为<br>
  formattedText = formattedText.replace(/\n/g, '<br>');

  return formattedText;
}

// 处理标签点击
const handleTagClick = (tag: string) => {
  router.push(`/search?keyword=${encodeURIComponent(tag)}&type=tag`);
}
</script>

<style lang="scss" scoped>
.photo-card {
  background-color: white;
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s;

  &:hover {
    transform: translateY(-0.25rem);
    box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
  }

  .photo-container {
    position: relative;
    overflow: hidden;

    .photo-image {
      width: 100%;
      aspect-ratio: 4/3;
    }

    .photo-swipe {
      width: 100%;
      aspect-ratio: 4/3;
    }

    .custom-indicator {
      position: absolute;
      right: 10px;
      bottom: 10px;
      padding: 2px 8px;
      font-size: 12px;
      background: rgba(0, 0, 0, 0.5);
      color: white;
      border-radius: 10px;
    }

    .error-placeholder {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      background-color: #f3f4f6;
      color: #9ca3af;
      aspect-ratio: 4/3;
    }

    .status-badge {
      position: absolute;
      top: 8px;
      right: 8px;
      z-index: 10;
    }

    .photo-overlay {
      position: absolute;
      inset: 0;
      background-color: rgba(0, 0, 0, 0.3);
      opacity: 0;
      transition: opacity 0.3s;
      display: flex;
      align-items: flex-end;

      &:hover {
        opacity: 1;
      }

      .photo-stats {
        display: flex;
        justify-content: flex-end;
        width: 100%;
        padding: 0.75rem;
        color: white;

        .stat-item {
          display: flex;
          align-items: center;
          margin-left: 0.75rem;

          .van-icon {
            margin-right: 0.25rem;
          }
        }
      }
    }
  }

  .photo-info {
    padding: 0.75rem;

    .photo-title {
      font-size: 0.875rem;
      font-weight: 500;
      margin-bottom: 0.5rem;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .photo-description {
      font-size: 0.75rem;
      color: #4b5563;
      margin-bottom: 0.5rem;
      max-height: 3rem;
      overflow: hidden;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;

      :deep(a) {
        color: #3b82f6;
        text-decoration: none;

        &:hover {
          text-decoration: underline;
        }
      }

      :deep(.mention) {
        color: #3b82f6;
      }

      :deep(.hashtag) {
        color: #8b5cf6;
      }
    }

    .photo-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 0.25rem;
      margin-bottom: 0.5rem;

      .tag-item {
        font-size: 0.75rem;
        color: #8b5cf6;
        background-color: #f3f4f6;
        padding: 0.125rem 0.375rem;
        border-radius: 0.25rem;
        cursor: pointer;

        &:hover {
          background-color: #e5e7eb;
        }
      }
    }

    .photo-meta {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .user-info {
        display: flex;
        align-items: center;
        cursor: pointer;

        .username {
          margin-left: 0.25rem;
          font-size: 0.75rem;
          color: #4b5563;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 100px;
        }
      }

      .action-buttons {
        display: flex;
        align-items: center;

        .van-icon {
          margin-left: 0.5rem;
          font-size: 1.125rem;
          cursor: pointer;
          color: #6b7280;

          &.liked {
            color: #e74c3c;
          }

          &.collected {
            color: #f39c12;
          }
        }
      }
    }
  }
}

// 响应式布局
@media (max-width: 640px) {
  .photo-card {
    .photo-info {
      .photo-meta {
        .user-info {
          .username {
            max-width: 80px;
          }
        }
      }
    }
  }
}
</style>
